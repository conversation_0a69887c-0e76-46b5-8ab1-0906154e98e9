{"models": [{"name": "flux1-canny-dev-lora", "filename": "flux1-canny-dev-lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flux1-canny-dev-lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: flux1-canny-dev-lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "黑格建筑效果表现FLUX_new", "filename": "黑格建筑效果表现FLUX_new.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/黑格建筑效果表现FLUX_new.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "building", "house", "building"], "description": "自动发现的Lora模型: 黑格建筑效果表现FLUX_new.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "比鲁斯商业建筑_V0.2", "filename": "比鲁斯商业建筑_V0.2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯商业建筑_V0.2.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "building", "house", "building"], "description": "自动发现的Lora模型: 比鲁斯商业建筑_V0.2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "flux-lora-NewShikumen", "filename": "flux-lora-NewShikumen.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flux-lora-NewShikumen.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: flux-lora-NewShikumen.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ck-flaani-flux", "filename": "ck-flaani-flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/ck-flaani-flux.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: ck-flaani-flux.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "flux_realism_lora", "filename": "flux_realism_lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flux_realism_lora.safetensors", "category": "style", "weight": 0.8, "trigger_words": ["写实", "realistic", "真实", "photorealistic"], "description": "自动发现的Lora模型: flux_realism_lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true}, {"name": "detail_aidmafluxproultra-FLUX-v0.1", "filename": "detail_aidmafluxproultra-FLUX-v0.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/detail_aidmafluxproultra-FLUX-v0.1.safetensors", "category": "detail", "weight": 0.8, "trigger_words": ["细节", "detail", "高清", "high quality"], "description": "自动发现的Lora模型: detail_aidmafluxproultra-FLUX-v0.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true}, {"name": "比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO", "filename": "比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: 比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "add-detail-xl", "filename": "add-detail-xl.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/add-detail-xl.safetensors", "category": "detail", "weight": 0.8, "trigger_words": ["细节", "detail", "高清", "high quality"], "description": "自动发现的Lora模型: add-detail-xl.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": false, "is_priority": true}, {"name": "Hyper-FLUX.1-dev-8steps-lora", "filename": "Hyper-FLUX.1-dev-8steps-lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Hyper-FLUX.1-dev-8steps-lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: Hyper-FLUX.1-dev-8steps-lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "加州别墅-1.0", "filename": "加州别墅-1.0.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/加州别墅-1.0.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: 加州别墅-1.0.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "比鲁斯F.1中古室内Medieval Ancient Style _V0.1", "filename": "比鲁斯F.1中古室内Medieval Ancient Style _V0.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯F.1中古室内Medieval Ancient Style _V0.1.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: 比鲁斯F.1中古室内Medieval Ancient Style _V0.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ASTRA_Flux_OC_Vbeta-2", "filename": "ASTRA_Flux_OC_Vbeta-2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/ASTRA_Flux_OC_Vbeta-2.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "building", "house", "building"], "description": "自动发现的Lora模型: ASTRA_Flux_OC_Vbeta-2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO", "filename": "比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: 比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "flat2100", "filename": "flat2100.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flat2100.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: flat2100.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "比鲁斯商业建筑modern architecture_V0.2", "filename": "比鲁斯商业建筑modern architecture_V0.2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯商业建筑modern architecture_V0.2.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "building", "house", "building"], "description": "自动发现的Lora模型: 比鲁斯商业建筑modern architecture_V0.2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "RA_curtain_wall_rank16_bf16", "filename": "RA_curtain_wall_rank16_bf16.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/RA_curtain_wall_rank16_bf16.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: RA_curtain_wall_rank16_bf16.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "Flux Turbo Lora_StarAi_Flux Turbo Lora", "filename": "Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: Flux Turbo Lora_StarAi_Flux Turbo Lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "flux1-depth-dev-lora", "filename": "flux1-depth-dev-lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flux1-depth-dev-lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: flux1-depth-dev-lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "outfit-generator", "filename": "outfit-generator.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/outfit-generator.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: outfit-generator.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "goodhands_<PERSON>_<PERSON><PERSON><PERSON>", "filename": "goodhands_Beta_Gtonero.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/goodhands_Beta_Gtonero.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: goodhands_Beta_Gtonero.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "artchitecture", "filename": "artchitecture.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/artchitecture.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: artchitecture.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ShallowDepth", "filename": "ShallowDepth.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/ShallowDepth.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: ShallowDepth.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "LoRA_NeoChineseModernismGarden", "filename": "LoRA_NeoChineseModernismGarden.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/LoRA_NeoChineseModernismGarden.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: LoRA_NeoChineseModernismGarden.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "Minimalism_Flux", "filename": "Minimalism_Flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Minimalism_Flux.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: Minimalism_Flux.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "Flux Flat Anime", "filename": "Flux Flat Anime.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Flux Flat Anime.safetensors", "category": "anime", "weight": 0.8, "trigger_words": ["动漫", "anime", "二次元", "manga"], "description": "自动发现的Lora模型: Flux Flat Anime.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "Animeniji_01", "filename": "Animeniji_01.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Animeniji_01.safetensors", "category": "anime", "weight": 0.8, "trigger_words": ["动漫", "anime", "二次元", "manga"], "description": "自动发现的Lora模型: Animeniji_01.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "niji_flux", "filename": "niji_flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/niji_flux.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: niji_flux.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "Niji_semi-realism_flux", "filename": "Niji_semi-realism_flux.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Niji_semi-realism_flux.safetensors", "category": "style", "weight": 0.8, "trigger_words": ["写实", "realistic", "真实", "photorealistic"], "description": "自动发现的Lora模型: Niji_semi-realism_flux.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true}, {"name": "比鲁斯Flux1中古室内Medieval Ancient Style _V0.1", "filename": "比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: 比鲁斯Flux1中古室内Medieval Ancient Style _V0.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "NeoChineseModernInterior", "filename": "NeoChineseModernInterior.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/NeoChineseModernInterior.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: NeoChineseModernInterior.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "artchitecture_Flux1", "filename": "artchitecture_Flux1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/artchitecture_Flux1.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: artchitecture_Flux1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "Anime_niji", "filename": "Anime_niji.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Anime_niji.safetensors", "category": "anime", "weight": 0.8, "trigger_words": ["动漫", "anime", "二次元", "manga"], "description": "自动发现的Lora模型: Anime_niji.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "flux-RA-NewShikumen", "filename": "flux-RA-NewShikumen.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/flux-RA-NewShikumen.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: flux-RA-NewShikumen.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "Flux_RA_curtainWwall_rk16_bf16", "filename": "Flux_RA_curtainWwall_rk16_bf16.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Flux_RA_curtainWwall_rk16_bf16.safetensors", "category": "other", "weight": 0.8, "trigger_words": [], "description": "自动发现的Lora模型: Flux_RA_curtainWwall_rk16_bf16.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "Detail Tweaker XL", "filename": "Detail Tweaker XL.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Detail Tweaker XL.safetensors", "category": "detail", "weight": 0.8, "trigger_words": ["细节", "detail", "高清", "high quality"], "description": "自动发现的Lora模型: Detail Tweaker XL.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true}, {"name": "Particle Vision", "filename": "Particle Vision.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Particle Vision.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["objects"], "description": "<p>🔥 <strong>Model Name:</strong> <strong>Liz<PERSON><PERSON><PERSON><PERSON> (粒子影像)</strong></p><p>💚 Dream of characters emerging from <strong>countless glowing green particles</strong>? This LoRA is your magic VFX tool!</p><p><strong>Super Easy Prompt Formula:</strong></p><p><code>liziyingxiang + [环境描述] + 无数闪烁着绿色的粒子中逐渐汇聚出[XXXX] + 该过程从[XX部位]开始，向上蔓延至全身 + [人物细节] + [背景细节]</code></p><p>Character Inscription Example：【<strong>liziyingxiang，</strong>昏黄的煤气灯光在迷蒙的蒸汽中弥漫。<strong>无数闪烁着绿色的粒子中逐渐汇聚出</strong>一位东方女性，其伫立在街头，身姿挺拔，神情坚毅。<strong>该过程从腿部开始，向上蔓延至全身，镜头环绕着她动态旋转，</strong>她身着一件深红色高领上衣，外套一件深棕色皮革马甲，下身是同色系的深红色皮革长裙，裙摆垂坠，富有质感。<strong>背景是</strong>高耸的哥特式建筑和巨大的工业管道，蒸汽缭绕，模糊了远处的景物，增添了浓郁的工业时代氛围与神秘感。】<br />Car Inscription Examples：【<strong>liziyingxiang</strong>，霓虹灯光照亮了雨后湿滑的未来都市街道，水面倒映着斑斓的光影。<strong>无数闪烁着蓝色的粒子中逐渐汇聚出</strong>一辆造型极致、线条凌厉的超跑静静伫立在街头中央，其金属车身被两侧的青色与品红色的霓虹灯光分割，呈现出迷幻的光影效果。<strong>该过程从车轮开始，向上蔓延至全车，</strong>低趴的车身极具攻击性，光滑的漆面反射着周围环境的炫目光彩，标志性的Y字形日间行车灯如猛兽之眼般锐利，前唇和侧裙点缀着暗金色饰条，增添了一抹奢华与未来感。整车仿佛是数字时代的幽灵骑士，穿梭在光怪陆离的赛博都市之中，散发出强烈的科技感与冰冷的机械魅力，完美融合了高性能跑车的极致美学与赛博朋克世界的视觉冲击。】<br /></p><p><span style=\"color:rgb(248, 250, 255)\">💡 </span><strong>适合场景：</strong><span style=\"color:rgb(248, 250, 255)\"> 科幻角色登场、能量觉醒、超能变身、未来战士、数字生命体... 想象力有多大，舞台就有多大！</span></p><p></p><p>🎯 <strong>Key Effects:</strong></p><ul><li><p><strong>Signature Green Particle Flow:</strong> Unique, fluid particle FX for instant sci-fi vibes!</p></li><li><p><strong>Dynamic Convergence:</strong> Particles spread from your chosen starting point (hand, foot, ground) upwards!</p></li><li><p><strong>Cinematic Camera Work:</strong> Automatic dynamic rotating shot around the character for maximum impact!</p></li><li><p><strong>Seamless Integration:</strong> Easily blend with your character designs and backgrounds!</p></li></ul><p></p><p>Have fun!</p><p></p>", "civitai_id": "1645617", "civitai_url": "https://civitai.com/models/1645617", "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "Hui-Style Architecture 徽派建筑风格", "filename": "Hui-Style Architecture 徽派建筑风格.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/Hui-Style Architecture 徽派建筑风格.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["art", "architecture", "buildings", "traditional art", "building"], "description": "<p>Hui-Style Architecture 徽派建筑风格 </p><p>Traditional Chinese Architectural Style 中国传统建筑风格</p>", "civitai_id": "863632", "civitai_url": "https://civitai.com/models/863632", "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "pytorch_lora_weights", "filename": "pytorch_lora_weights.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/lcm/SD1.5/pytorch_lora_weights.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["safetensors", "pytorch", "weights"], "description": "自动发现的Lora模型: pytorch_lora_weights.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "sdxl_lightning_8step_lora", "filename": "sdxl_lightning_8step_lora.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL-Lightning/sdxl_lightning_8step_lora.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["safetensors", "sdxl", "step", "lightning"], "description": "自动发现的Lora模型: sdxl_lightning_8step_lora.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ASTRA_KC_XL_RSD_Landscape_V1", "filename": "ASTRA_KC_XL_RSD_Landscape_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_XL_RSD_Landscape_V1.safetensors", "category": "landscape", "weight": 0.8, "trigger_words": ["landscape", "safetensors", "rsd", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_XL_RSD_Landscape_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1", "filename": "ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["interior", "bedroom", "astra", "living", "safetensors", "minimalism"], "description": "自动发现的Lora模型: ASTRA_KC_Interior_Living_Bedroom_Minimalism_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ASTRA_XL_Artistic_Rendering_Style_V2", "filename": "ASTRA_XL_Artistic_Rendering_Style_V2.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_XL_Artistic_Rendering_Style_V2.safetensors", "category": "style", "weight": 0.8, "trigger_words": ["artistic", "style", "rendering", "astra", "safetensors"], "description": "自动发现的Lora模型: ASTRA_XL_Artistic_Rendering_Style_V2.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ASTRA_XL_AerialView_Photography_V1", "filename": "ASTRA_XL_AerialView_Photography_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_XL_AerialView_Photography_V1.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["safetensors", "aerialview", "photography", "astra"], "description": "自动发现的Lora模型: ASTRA_XL_AerialView_Photography_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ASTRA_KC_XL_PlantGroups_V1", "filename": "ASTRA_KC_XL_PlantGroups_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_XL_PlantGroups_V1.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["safetensors", "plantgroups", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_XL_PlantGroups_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ASTRA_KC_Urban_Renewal_V1.1", "filename": "ASTRA_KC_Urban_Renewal_V1.1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_Urban_Renewal_V1.1.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["urban", "renewal", "safetensors", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_Urban_Renewal_V1.1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}, {"name": "ASTRA_XL_OC_V3", "filename": "ASTRA_XL_OC_V3.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_XL_OC_V3.safetensors", "category": "architecture", "weight": 0.8, "trigger_words": ["建筑", "architecture", "building", "渲染", "rendering"], "description": "专业建筑效果图渲染模型", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": true}, {"name": "ASTRA_KC_XL_Residential_V1", "filename": "ASTRA_KC_XL_Residential_V1.safetensors", "file_path": "/home/<USER>/Workspace/ComfyUI/models/loras/SDXL/ASTRA_KC_XL_Residential_V1.safetensors", "category": "other", "weight": 0.8, "trigger_words": ["residential", "safetensors", "astra"], "description": "自动发现的Lora模型: ASTRA_KC_XL_Residential_V1.safetensors", "civitai_id": null, "civitai_url": null, "rating": null, "downloads": null, "is_local": true, "is_active": true, "is_priority": false}]}