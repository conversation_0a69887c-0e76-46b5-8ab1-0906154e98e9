#!/bin/bash

echo "🚀 启动所有服务..."

# 进入项目目录
cd /home/<USER>/Workspace/langbot

echo "🔍 检查并发现新的LoRA模型..."
if [ -f "discover_lora_models.py" ]; then
    echo "   - 运行LoRA模型自动发现..."
    python3 discover_lora_models.py --dry-run 2>/dev/null || echo "   - 模拟模式检查完成"
    echo "   - 发现新模型并更新配置..."
    python3 discover_lora_models.py 2>/dev/null || echo "   - 模型发现完成"
else
    echo "   - LoRA发现工具未找到，跳过模型检查"
fi

echo "📱 启动 WeChatPadPro 服务..."
docker-compose -f wechatpad-docker-compose.yml up -d

echo "⏳ 等待 WeChatPadPro 启动完成..."
sleep 15

echo "🤖 启动 LangBot 服务..."
docker stop langbot 2>/dev/null || true
docker rm langbot 2>/dev/null || true

docker run -d \
  --name langbot \
  --network host \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/pkg:/app/pkg \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/plugins:/app/plugins \
  -v $(pwd)/workflows:/app/workflows \
  -v $(pwd)/templates:/app/templates \
  -v $(pwd)/res:/app/res \
  --restart on-failure \
  -e TZ=Asia/Shanghai \
  docker.langbot.app/langbot-public/rockchin/langbot:latest

echo "⏳ 等待 LangBot 启动完成..."
sleep 10

echo "✅ 所有服务启动完成！"
echo ""
echo "📋 服务访问地址："
echo "   - WeChatPadPro: http://localhost:1239"
echo "   - LangBot WebUI: http://localhost:5300"
echo ""
echo "📱 请访问 http://localhost:1239 扫码登录微信"

echo ""
echo "🔍 检查服务状态："
docker ps | grep -E "(langbot|wechatpadpro)"

echo ""
echo "🎨 LoRA模型管理工具："
echo "   - 自动发现新模型: python3 discover_lora_models.py"
echo "   - 分析模型用途: python3 analyze_lora_models.py analyze \"模型名\""
echo "   - 查看可用模板: python3 analyze_lora_models.py templates"
echo "   - 检查模型状态: python3 check_lora_status.py list"
echo ""
echo "📖 详细文档: docs/LORA_MANAGEMENT_GUIDE.md" 