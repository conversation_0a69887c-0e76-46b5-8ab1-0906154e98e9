"""
Kontext 本地模式工作流执行器
直接调用本地ComfyUI环境
"""

import json
import asyncio
import aiohttp
import base64
import os
import tempfile
from typing import Dict, Any, List, Optional
from .kontext_base_executor import BaseKontextExecutor
from pkg.core.workflow.manager_base import WorkflowResult


class LocalKontextExecutor(BaseKontextExecutor):
    def __init__(self, api_url: str = "http://localhost:8188", timeout: int = 180):
        self.api_url = api_url
        self.timeout = timeout
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建aiohttp会话"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def execute_workflow(self, prompt: str, query: Any, *args, **kwargs) -> WorkflowResult:
        try:
            print(f"🔍 [DEBUG] LocalKontextExecutor.execute_workflow 开始")
            print(f"🔍 [DEBUG] - 工作流文件: {prompt}")
            print(f"🔍 [DEBUG] - 参数类型: {type(query)}")
            if isinstance(query, dict):
                print(f"🔍 [DEBUG] - 参数键: {list(query.keys())}")
                if 'prompt' in query:
                    print(f"🔍 [DEBUG] - 提示词: {query['prompt']}")
                if 'images' in query:
                    images = query['images']
                    print(f"🔍 [DEBUG] - 图片数量: {len(images)}")
                    for i, img in enumerate(images):
                        print(f"🔍 [DEBUG] - 图片{i+1}: {len(img)} bytes")
            else:
                # 避免输出二进制数据，只显示类型和基本信息
                query_type = str(type(query))
                print(f"🔍 [DEBUG] - 参数类型: {query_type}")

                # 安全地检查对象属性，避免输出二进制数据
                if hasattr(query, '__dict__'):
                    attrs = []
                    for key, value in query.__dict__.items():
                        if isinstance(value, bytes):
                            attrs.append(f"{key}: {len(value)} bytes")
                        elif isinstance(value, list) and value and isinstance(value[0], bytes):
                            attrs.append(f"{key}: {len(value)} images")
                        elif isinstance(value, str) and len(value) > 50:
                            attrs.append(f"{key}: '{value[:50]}...'")
                        else:
                            attrs.append(f"{key}: {value}")
                    print(f"🔍 [DEBUG] - 参数属性: {attrs}")
                else:
                    print(f"🔍 [DEBUG] - 参数内容: 无法安全显示")

            # 1. 加载工作流文件
            workflow_data = await self._load_workflow_file(prompt)  # prompt作为workflow_file
            if not workflow_data:
                error_msg = f'工作流文件不存在: {prompt}'
                print(f"🔍 [DEBUG] 错误: {error_msg}")
                return WorkflowResult(success=False, error_message=error_msg)

            print(f"🔍 [DEBUG] 工作流文件加载成功，节点数: {len(workflow_data)}")

            # 2. 更新工作流参数
            updated_workflow = self._update_workflow_params(workflow_data, query)
            print(f"🔍 [DEBUG] 工作流参数更新完成")
            
            # 3. 上传图片（如果有）
            if 'images' in query and query['images']:
                print(f"🔍 [DEBUG] 开始上传 {len(query['images'])} 张图片")
                updated_workflow = await self._upload_images_to_workflow(updated_workflow, query['images'])
                print(f"🔍 [DEBUG] 图片上传完成")
            else:
                print(f"🔍 [DEBUG] 无图片需要上传")

            # 4. 提交工作流到ComfyUI
            print(f"🔍 [DEBUG] 开始提交工作流到ComfyUI")
            session = await self._get_session()
            prompt_id = await self._submit_workflow(session, updated_workflow)
            if not prompt_id:
                error_msg = '提交工作流失败'
                print(f"🔍 [DEBUG] 错误: {error_msg}")
                return WorkflowResult(success=False, error_message=error_msg)

            print(f"🔍 [DEBUG] 工作流提交成功，prompt_id: {prompt_id}")

            # 5. 等待执行完成
            print(f"🔍 [DEBUG] 开始等待工作流执行完成")
            image_data = await self._wait_for_completion(prompt_id)
            if not image_data:
                error_msg = '工作流执行超时或失败'
                print(f"🔍 [DEBUG] 错误: {error_msg}")
                return WorkflowResult(success=False, error_message=error_msg)

            print(f"🔍 [DEBUG] 工作流执行成功，图片大小: {len(image_data)} bytes")
            
            return WorkflowResult(success=True, image_data=image_data, metadata={
                'prompt_id': prompt_id,
                'workflow_file': prompt
            })
            
        except Exception as e:
            error_msg = f'执行工作流失败: {str(e)}'
            print(f"🔍 [DEBUG] 异常: {error_msg}")
            print(f"🔍 [DEBUG] 异常类型: {type(e).__name__}")
            import traceback
            print(f"🔍 [DEBUG] 异常堆栈: {traceback.format_exc()}")
            return WorkflowResult(success=False, error_message=error_msg)
    
    async def _load_workflow_file(self, workflow_file: str) -> Optional[Dict[str, Any]]:
        """加载工作流文件"""
        try:
            # 检查是否是完整路径
            if os.path.exists(workflow_file):
                file_path = workflow_file
            else:
                # 假设是相对于workflows目录的路径
                file_path = os.path.join('workflows', workflow_file)
            
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            print(f"加载工作流文件失败: {e}")
            return None
    
    def _update_workflow_params(self, workflow: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """更新工作流参数"""
        updated_workflow = workflow.copy()
        
        # 更新提示词
        if 'prompt' in params:
            for node_id, node_data in updated_workflow.items():
                if node_data.get("class_type") == "CLIPTextEncode":
                    # 检查是否是正面提示词节点
                    if "positive" in node_data.get("_meta", {}).get("title", "").lower():
                        node_data["inputs"]["text"] = params['prompt']
                    # 检查是否是负面提示词节点
                    elif "negative" in node_data.get("_meta", {}).get("title", "").lower():
                        node_data["inputs"]["text"] = params.get('negative_prompt', '')
        
        # 更新生成参数
        if 'guidance' in params or 'steps' in params or 'seed' in params:
            for node_id, node_data in updated_workflow.items():
                if node_data.get("class_type") == "KSampler":
                    if 'steps' in params:
                        node_data["inputs"]["steps"] = params['steps']
                    if 'guidance' in params:
                        node_data["inputs"]["cfg"] = params['guidance']
                    if 'seed' in params:
                        node_data["inputs"]["seed"] = params['seed']
        
        # 更新FluxKontextProImageNode参数
        for node_id, node_data in updated_workflow.items():
            if node_data.get("class_type") == "FluxKontextProImageNode":
                if 'prompt' in params:
                    node_data["inputs"]["prompt"] = params['prompt']
                if 'aspect_ratio' in params:
                    node_data["inputs"]["aspect_ratio"] = params['aspect_ratio']
                if 'guidance' in params:
                    node_data["inputs"]["guidance"] = params['guidance']
                if 'steps' in params:
                    node_data["inputs"]["steps"] = params['steps']
                if 'seed' in params:
                    node_data["inputs"]["seed"] = params['seed']
                break
        
        return updated_workflow
    
    async def _upload_images_to_workflow(self, workflow: Dict[str, Any], images: List[bytes]) -> Dict[str, Any]:
        """将图片上传到工作流"""
        try:
            session = await self._get_session()
            
            # 查找图片输入节点
            image_input_nodes = []
            for node_id, node_data in workflow.items():
                if node_data.get("class_type") in ["LoadImage", "easy loadImageBase64"]:
                    image_input_nodes.append((node_id, node_data))
            
            # 按标题排序，确保正确的映射顺序
            image_input_nodes.sort(key=lambda x: x[1].get("_meta", {}).get("title", ""))
            
            # 上传图片并更新工作流
            for i, (node_id, node_data) in enumerate(image_input_nodes):
                if i < len(images):
                    # 上传图片到ComfyUI
                    image_name = await self._upload_image(session, images[i], f"kontext_input_{i+1}")
                    if image_name:
                        if node_data.get("class_type") == "LoadImage":
                            node_data["inputs"]["image"] = image_name
                        elif node_data.get("class_type") == "easy loadImageBase64":
                            # 对于base64节点，需要将图片转换为base64
                            import base64
                            image_base64 = base64.b64encode(images[i]).decode('utf-8')
                            node_data["inputs"]["base64_data"] = image_base64
            
            return workflow
            
        except Exception as e:
            print(f"上传图片失败: {e}")
            return workflow
    
    async def _upload_image(self, session: aiohttp.ClientSession, image_data: bytes, filename: str) -> Optional[str]:
        """上传单张图片到ComfyUI"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(image_data)
                temp_file_path = temp_file.name
            
            try:
                # 上传文件
                with open(temp_file_path, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field('image', f, filename=filename)
                    
                    async with session.post(f"{self.api_url}/upload/image", data=data) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result.get('name', filename)
                        else:
                            print(f"上传图片失败: {response.status}")
                            return None
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            print(f"上传图片出错: {e}")
            return None
    
    async def _submit_workflow(self, session: aiohttp.ClientSession, workflow: Dict[str, Any]) -> Optional[str]:
        """提交工作流到ComfyUI"""
        try:
            async with session.post(
                f"{self.api_url}/prompt",
                json={"prompt": workflow},
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('prompt_id')
                else:
                    print(f"提交工作流失败: {response.status}")
                    return None
        except Exception as e:
            print(f"提交工作流出错: {e}")
            return None
    
    async def _wait_for_completion(self, prompt_id: str) -> Optional[bytes]:
        """等待工作流完成并获取图片数据"""
        try:
            session = await self._get_session()
            start_time = asyncio.get_event_loop().time()
            
            while asyncio.get_event_loop().time() - start_time < self.timeout:
                # 检查工作流状态
                async with session.get(f"{self.api_url}/history/{prompt_id}") as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if prompt_id in result:
                            prompt_data = result[prompt_id]
                            status = prompt_data.get('status', {})
                            status_str = status.get('status_str', 'unknown')
                            
                            if status_str == 'error':
                                print(f"工作流执行出错: {prompt_id}")
                                return None
                            elif status_str == 'completed':
                                # 获取生成的图片
                                outputs = prompt_data.get('outputs', {})
                                for node_id, node_output in outputs.items():
                                    if 'images' in node_output:
                                        images = node_output['images']
                                        if images:
                                            # 下载第一张图片
                                            image_filename = images[0]['filename']
                                            return await self._download_image(session, image_filename)
                                return None
                    
                # 等待1秒后重试
                await asyncio.sleep(1)
            
            print(f"工作流执行超时: {prompt_id}")
            return None
            
        except Exception as e:
            print(f"等待工作流完成出错: {e}")
            return None
    
    async def _download_image(self, session: aiohttp.ClientSession, filename: str) -> Optional[bytes]:
        """下载生成的图片"""
        try:
            async with session.get(f"{self.api_url}/view?filename={filename}") as response:
                if response.status == 200:
                    return await response.read()
                else:
                    print(f"下载图片失败: {response.status}")
                    return None
        except Exception as e:
            print(f"下载图片出错: {e}")
            return None 