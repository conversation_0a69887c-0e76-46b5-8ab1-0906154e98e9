#!/bin/bash

# ComfyUI服务重启脚本（应用内存修复）
# 解决Flux模型内存泄漏和工作流验证失败问题

set -e

echo "🔧 ComfyUI服务重启脚本（应用内存修复）"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查ComfyUI进程
check_comfyui_process() {
    if pgrep -f "comfyui" > /dev/null; then
        return 0
    else
        return 1
    fi
}

# 停止ComfyUI服务
stop_comfyui() {
    log_info "停止ComfyUI服务..."
    
    if check_comfyui_process; then
        # 尝试优雅停止
        pkill -f "comfyui" || true
        sleep 2
        
        # 强制停止（如果还在运行）
        if check_comfyui_process; then
            log_warning "优雅停止失败，强制停止..."
            pkill -9 -f "comfyui" || true
            sleep 1
        fi
        
        if check_comfyui_process; then
            log_error "无法停止ComfyUI服务"
            return 1
        else
            log_success "ComfyUI服务已停止"
        fi
    else
        log_info "ComfyUI服务未运行"
    fi
}

# 清理内存和缓存
cleanup_memory() {
    log_info "清理内存和缓存..."
    
    # 清理Python缓存
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    # 清理临时文件
    rm -rf temp/workflow_cache/* 2>/dev/null || true
    rm -rf temp/*.tmp 2>/dev/null || true
    
    # 清理日志文件（保留最近的文件）
    find logs -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    log_success "内存和缓存清理完成"
}

# 应用内存修复
apply_memory_fixes() {
    log_info "应用内存修复..."
    
    # 运行内存修复脚本
    if [ -f "scripts/fix_comfyui_memory_issues.py" ]; then
        python3 scripts/fix_comfyui_memory_issues.py
        log_success "内存修复应用完成"
    else
        log_warning "内存修复脚本不存在，跳过"
    fi
}

# 启动ComfyUI服务
start_comfyui() {
    log_info "启动ComfyUI服务..."
    
    # 检查ComfyUI目录
    if [ ! -d "/home/<USER>/Workspace/ComfyUI" ]; then
        log_error "ComfyUI目录不存在: /home/<USER>/Workspace/ComfyUI"
        return 1
    fi
    
    # 切换到ComfyUI目录
    cd /home/<USER>/Workspace/ComfyUI
    
    # 启动ComfyUI（后台运行）
    nohup python main.py --listen 0.0.0.0 --port 8188 > comfyui.log 2>&1 &
    
    # 等待服务启动
    sleep 5
    
    # 检查服务是否启动成功
    if check_comfyui_process; then
        log_success "ComfyUI服务启动成功"
        log_info "服务地址: http://localhost:8188"
        log_info "日志文件: /home/<USER>/Workspace/ComfyUI/comfyui.log"
    else
        log_error "ComfyUI服务启动失败"
        return 1
    fi
}

# 验证服务状态
verify_service() {
    log_info "验证服务状态..."
    
    # 等待服务完全启动
    sleep 10
    
    # 检查HTTP响应
    if curl -s http://localhost:8188 > /dev/null; then
        log_success "ComfyUI服务响应正常"
    else
        log_warning "ComfyUI服务响应异常，可能需要更多时间启动"
    fi
    
    # 检查进程状态
    if check_comfyui_process; then
        log_success "ComfyUI进程运行正常"
    else
        log_error "ComfyUI进程未运行"
        return 1
    fi
}

# 显示状态信息
show_status() {
    log_info "服务状态信息:"
    
    if check_comfyui_process; then
        echo "  ✅ ComfyUI进程: 运行中"
        echo "  🌐 服务地址: http://localhost:8188"
        echo "  📝 日志文件: /home/<USER>/Workspace/ComfyUI/comfyui.log"
        
        # 显示进程信息
        echo "  📊 进程信息:"
        ps aux | grep comfyui | grep -v grep | while read line; do
            echo "    $line"
        done
    else
        echo "  ❌ ComfyUI进程: 未运行"
    fi
}

# 主函数
main() {
    echo "开始执行ComfyUI服务重启流程..."
    
    # 1. 停止服务
    stop_comfyui
    
    # 2. 清理内存和缓存
    cleanup_memory
    
    # 3. 应用内存修复
    apply_memory_fixes
    
    # 4. 启动服务
    start_comfyui
    
    # 5. 验证服务
    verify_service
    
    # 6. 显示状态
    show_status
    
    echo ""
    log_success "ComfyUI服务重启完成！"
    echo ""
    echo "📋 后续建议:"
    echo "1. 测试图片生成功能"
    echo "2. 监控内存使用情况"
    echo "3. 检查日志文件中的错误信息"
    echo "4. 如果仍有问题，考虑重启整个系统"
}

# 错误处理
trap 'log_error "脚本执行失败，退出码: $?"' ERR

# 执行主函数
main "$@" 