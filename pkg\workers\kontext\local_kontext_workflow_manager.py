"""
[二次开发] Local Kontext 工作流管理器

封装本地 Kontext 工作流的主要业务逻辑，聚合执行器、参数处理、多图处理等专有功能。
结构与 FluxWorkflowManager 保持一致。

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：本地Kontext工作流的统一管理和执行
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-04 业务逻辑实现
- 依赖关系：依赖langbot的工作流基类和ComfyUI客户端
"""

import logging
from typing import Dict, Any, List, Optional
from .local_executor import LocalKontextExecutor
from .kontext_workflow_models import KontextParameters, KontextExecutionResult, KontextWorkflowConfig
from .multi_image_handler import MultiImageHandler
from .custom_nodes import CustomNodeHandler
from pkg.core.workflow.manager_base import BaseWorkflowManager, WorkflowResult

class LocalKontextWorkflowManager(BaseWorkflowManager):
    """
    本地 Kontext 工作流管理器
    """
    def __init__(self, api_url: str = "http://localhost:8188", timeout: int = 180):
        self.logger = logging.getLogger(__name__)
        self.executor = LocalKontextExecutor(api_url=api_url, timeout=timeout)
        self.multi_image_handler = MultiImageHandler()
        self.custom_node_handler = CustomNodeHandler()
        
        # 初始化工作流配置
        self.kontext_workflows = {
            1: KontextWorkflowConfig(
                workflow_file="kontext_local_single_image.json",
                image_input_count=1,
                description="单图Kontext编辑"
            ),
            2: KontextWorkflowConfig(
                workflow_file="kontext_local_double_images.json", 
                image_input_count=2,
                description="双图Kontext编辑"
            ),
            3: KontextWorkflowConfig(
                workflow_file="kontext_local_triple_images.json",
                image_input_count=3,
                description="三图Kontext编辑"
            )
        }

    async def generate_image(self, user_text: str, params: Dict[str, Any], images: Optional[List[bytes]] = None) -> WorkflowResult:
        result = await self.executor.execute(user_text, params, images)
        image_bytes = None
        if hasattr(result, 'image_data') and isinstance(result.image_data, bytes):
            image_bytes = result.image_data
        elif isinstance(result, dict) and isinstance(result.get('image_data'), bytes):
            image_bytes = result.get('image_data')
        images_list = [image_bytes] if image_bytes else []
        return WorkflowResult(
            success=result.success if hasattr(result, 'success') else result.get('success', False),
            images=images_list,
            metadata=result.get_generation_info() if hasattr(result, 'get_generation_info') else {},
            workflow_type="kontext",
            error_message=getattr(result, 'error_message', result.get('error', None) if isinstance(result, dict) else None)
        )

    async def submit_workflow(self, workflow_data: Dict[str, Any], images: Optional[List[bytes]] = None) -> WorkflowResult:
        result = await self.executor.execute_workflow(workflow_data, images)
        image_bytes = None
        if hasattr(result, 'image_data') and isinstance(result.image_data, bytes):
            image_bytes = result.image_data
        elif isinstance(result, dict) and isinstance(result.get('image_data'), bytes):
            image_bytes = result.get('image_data')
        images_list = [image_bytes] if image_bytes else []
        return WorkflowResult(
            success=result.success if hasattr(result, 'success') else result.get('success', False),
            images=images_list,
            metadata=result.get_generation_info() if hasattr(result, 'get_generation_info') else {},
            workflow_type="kontext",
            error_message=getattr(result, 'error_message', result.get('error', None) if isinstance(result, dict) else None)
        )

    async def execute_workflow(self, prompt: str, query: Any) -> WorkflowResult:
        # 伪实现，实际逻辑请补充
        try:
            # ... 执行工作流逻辑
            return WorkflowResult(success=True, image_data=b"fake", metadata={})
        except Exception as e:
            return WorkflowResult(success=False, error_message=str(e))

    async def get_generation_info(self, task_id: str) -> WorkflowResult:
        # 伪实现，实际逻辑请补充
        return WorkflowResult(success=True, metadata={"task_id": task_id})

    async def close(self) -> None:
        # 预留资源释放接口
        pass

    def select_workflow(self, image_count: int) -> KontextWorkflowConfig:
        """根据图片数量选择合适的工作流"""
        
        if image_count <= 0:
            raise ValueError("Kontext工作流至少需要一张图片")
        
        # 限制最大图片数量为3
        effective_count = min(image_count, 3)
        
        config = self.kontext_workflows[effective_count]
        self.logger.info(f"选择Kontext工作流: {config.description} (输入图片: {effective_count}张)")
        
        # 如果用户提供了超过3张图片，记录警告
        if image_count > 3:
            self.logger.warning(f"用户提供了{image_count}张图片，但Kontext工作流最多支持3张图片，将使用前3张图片")
        
        return config

    # 可根据需要扩展更多管理功能，如参数分析、节点处理等 