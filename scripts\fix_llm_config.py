#!/usr/bin/env python3
"""
[二次开发] LLM配置修复脚本
自动配置langbot的LLM模型，解决ComfyUI工作流中LLM调用失败的问题

开发说明：
- 此脚本为二次开发代码，不属于langbot原生代码
- 功能：自动从config/llm_config.yaml读取配置并添加到langbot数据库
- 维护者：开发团队
- 最后更新：2025-01-09
- 相关任务：修复LLM配置问题
- 依赖关系：需要langbot容器运行
"""

import asyncio
import json
import sqlite3
import uuid
import yaml
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class LLMConfigFixer:
    def __init__(self):
        self.db_path = "data/langbot.db"
        self.config_path = "config/llm_config.yaml"
        
    def load_llm_config(self):
        """加载LLM配置文件"""
        if not os.path.exists(self.config_path):
            print(f"❌ 配置文件不存在: {self.config_path}")
            return None
            
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def get_db_connection(self):
        """获取数据库连接"""
        if not os.path.exists(self.db_path):
            print(f"❌ 数据库文件不存在: {self.db_path}")
            print("请确保langbot容器正在运行并已初始化数据库")
            return None
            
        return sqlite3.connect(self.db_path)
    
    def check_existing_models(self, conn):
        """检查现有模型"""
        cursor = conn.cursor()
        cursor.execute("SELECT uuid, name, requester FROM llm_models")
        models = cursor.fetchall()
        
        print(f"📋 现有LLM模型 ({len(models)}个):")
        for model in models:
            print(f"  - {model[1]} ({model[2]}) - UUID: {model[0]}")
        
        return models
    
    def add_deepseek_model(self, conn, config):
        """添加DeepSeek模型"""
        deepseek_config = config.get('deepseek', {})
        if not deepseek_config:
            print("❌ 配置文件中未找到deepseek配置")
            return None
            
        model_uuid = str(uuid.uuid4())
        model_data = {
            'uuid': model_uuid,
            'name': 'DeepSeek Chat',
            'description': '深度求索聊天模型 - 自动配置',
            'requester': 'deepseek-chat-completions',
            'requester_config': json.dumps({
                'base_url': deepseek_config.get('endpoint', 'https://api.deepseek.com/v1'),
                'timeout': 120
            }),
            'api_keys': json.dumps([deepseek_config.get('api_key', '')]),
            'abilities': json.dumps(['chat']),
            'extra_args': json.dumps({})
        }
        
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO llm_models (uuid, name, description, requester, requester_config, api_keys, abilities, extra_args)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            model_data['uuid'],
            model_data['name'], 
            model_data['description'],
            model_data['requester'],
            model_data['requester_config'],
            model_data['api_keys'],
            model_data['abilities'],
            model_data['extra_args']
        ))
        
        conn.commit()
        print(f"✅ 已添加DeepSeek模型: {model_uuid}")
        return model_uuid
    
    def update_default_pipeline(self, conn, model_uuid):
        """更新默认流水线配置"""
        cursor = conn.cursor()
        
        # 获取默认流水线
        cursor.execute("SELECT uuid, config FROM legacy_pipelines WHERE is_default = 1")
        pipeline = cursor.fetchone()
        
        if not pipeline:
            print("❌ 未找到默认流水线")
            return False
            
        pipeline_uuid, config_str = pipeline
        config = json.loads(config_str)
        
        # 更新模型配置
        if 'ai' not in config:
            config['ai'] = {}
        if 'local-agent' not in config['ai']:
            config['ai']['local-agent'] = {}
            
        config['ai']['local-agent']['model'] = model_uuid
        
        # 保存更新后的配置
        cursor.execute(
            "UPDATE legacy_pipelines SET config = ? WHERE uuid = ?",
            (json.dumps(config), pipeline_uuid)
        )
        
        conn.commit()
        print(f"✅ 已更新默认流水线配置: {pipeline_uuid}")
        return True
    
    def verify_configuration(self, conn):
        """验证配置"""
        cursor = conn.cursor()
        
        # 检查默认流水线的模型配置
        cursor.execute("SELECT config FROM legacy_pipelines WHERE is_default = 1")
        result = cursor.fetchone()
        
        if result:
            config = json.loads(result[0])
            model_uuid = config.get('ai', {}).get('local-agent', {}).get('model', '')
            
            if model_uuid:
                # 检查模型是否存在
                cursor.execute("SELECT name FROM llm_models WHERE uuid = ?", (model_uuid,))
                model = cursor.fetchone()
                
                if model:
                    print(f"✅ 配置验证成功: 默认流水线已关联模型 '{model[0]}'")
                    return True
                else:
                    print(f"❌ 配置错误: 模型UUID {model_uuid} 不存在")
            else:
                print("❌ 配置错误: 默认流水线未配置模型")
        
        return False
    
    def run(self):
        """运行修复脚本"""
        print("🔧 开始修复LLM配置...")
        
        # 1. 加载配置文件
        config = self.load_llm_config()
        if not config:
            return False
            
        # 2. 连接数据库
        conn = self.get_db_connection()
        if not conn:
            return False
            
        try:
            # 3. 检查现有模型
            existing_models = self.check_existing_models(conn)
            
            # 4. 检查是否已有DeepSeek模型
            deepseek_exists = any('deepseek' in model[2].lower() for model in existing_models)
            
            if deepseek_exists:
                print("ℹ️ 已存在DeepSeek模型，跳过添加")
                # 使用现有的DeepSeek模型
                for model in existing_models:
                    if 'deepseek' in model[2].lower():
                        model_uuid = model[0]
                        break
            else:
                # 5. 添加DeepSeek模型
                model_uuid = self.add_deepseek_model(conn, config)
                if not model_uuid:
                    return False
            
            # 6. 更新默认流水线
            if self.update_default_pipeline(conn, model_uuid):
                # 7. 验证配置
                if self.verify_configuration(conn):
                    print("\n🎉 LLM配置修复完成！")
                    print("\n📝 后续步骤:")
                    print("1. 重启langbot容器: docker restart langbot")
                    print("2. 测试LLM功能: 发送 'aigen 测试图片' 到微信")
                    print("3. 查看日志: docker logs langbot --tail 50")
                    return True
            
            return False
            
        finally:
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 LangBot LLM配置修复工具")
    print("=" * 60)
    
    fixer = LLMConfigFixer()
    success = fixer.run()
    
    if success:
        print("\n✅ 修复成功！")
        exit(0)
    else:
        print("\n❌ 修复失败！")
        exit(1)

if __name__ == "__main__":
    main()
