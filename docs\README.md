# 📖 LangBot 文档中心

欢迎来到LangBot文档中心！这里包含了项目的完整文档，帮助您快速了解和使用LangBot。

## 🚀 快速开始

- [快速启动指南](./deployment/QUICK_START.md) - 5分钟快速部署
- [开发环境搭建](./deployment/DEV_GUIDE.md) - 开发环境配置
- [Docker开发指南](./deployment/DOCKER_DEVELOPMENT.md) - Docker容器化部署

## 🏗️ 系统架构

- [当前系统架构](./architecture/CURRENT_SYSTEM_ARCHITECTURE.md) - **重构后的完整系统架构**
- [代码文件功能指南](./CODE_FILES_FUNCTION_GUIDE.md) - **详细的代码文件功能说明**
- [系统重构总结](./REFACTORING_SUMMARY.md) - **重构过程、成果和影响**
- [两级路由架构](./architecture/TWO_LEVEL_ROUTING_ARCHITECTURE.md) - 智能路由系统设计
- [AIGEN LLM智能路由](./AIGEN_LLM_ROUTING_GUIDE.md) - AIGEN管道LLM智能路由指南
- [统一路由系统PRD](./architecture/PRD-********-UnifiedRoutingSystem.md) - 路由系统产品需求

## 🎨 功能集成

- [ComfyUI集成指南](./COMFYUI_INTEGRATION.md) - ComfyUI图像生成集成
- [ComfyUI API密钥指南](./COMFYUI_API_KEY_SUCCESS_GUIDE.md) - API密钥配置
- [ComfyUI官方API指南](./COMFYUI_OFFICIAL_API_KEY_GUIDE.md) - 官方API使用
- [LoRA管理指南](./LORA_MANAGEMENT_GUIDE.md) - LoRA模型管理
- [LoRA使用策略](./LORA_USAGE_STRATEGY.md) - LoRA使用最佳实践
- [LoRA工具快速参考](./LORA_TOOLS_QUICK_REFERENCE.md) - LoRA工具使用快速参考

## 📋 用户指南

- [用户交互流程](./USER_INTERACTION_FLOW.md) - 用户使用流程说明
- [Kontext用户指南](./KONTEXT_USER_GUIDE.md) - Kontext工作流使用
- [Kontext重复生成指南](./KONTEXT_REPEAT_GENERATION_GUIDE.md) - 重复生成功能
- [Kontext引用消息状态](./KONTEXT_QUOTED_MESSAGE_STATUS.md) - 引用消息处理
- [帮助命令指南](./HELP_COMMAND_GUIDE.md) - 命令使用说明

## 🔧 开发指南

- [二次开发集成指南](./二次开发集成指南.md) - 二次开发说明
- [二次开发索引](./SECONDARY_DEVELOPMENT_INDEX.md) - 开发索引和参考
- [工作流扩展指南](./workflow-extension-guide.md) - 自定义工作流开发
- [工作流管理指南](./workflow-management-guide.md) - 工作流管理
- [快速开发参考](./QUICK_DEV_REFERENCE.md) - 开发快速参考
- [贡献指南](./CONTRIBUTING.md) - 如何参与项目贡献

## 🔧 部署运维

- [Docker Compose修复](./deployment/DOCKER_COMPOSE_FIX.md) - Docker兼容性问题解决
- [服务器重启指南](./deployment/SERVER_RESTART_GUIDE.md) - 服务重启说明
- [管理员同步指南](./admin-sync-guide.md) - 管理员功能说明
- [Ubuntu微信设置](./ubuntu_wechat_setup.md) - Ubuntu环境配置

## 📋 规划文档

- [开发计划](./planning/DEVELOPMENT_PLAN.md) - 项目开发计划
- [开发路线图](./planning/DEVELOPMENT_ROADMAP.md) - 功能路线图

## 📡 API文档

- [API规范 V1](./api-specs/API_V1.md) - REST API接口文档

## 🔍 故障排除

- [快速参考](./troubleshooting/quick-reference.md) - 常见问题快速解决
- [网络问题排除](./troubleshooting/langbot-wechatpad-network-issue.md) - 网络连接故障

## 🧪 测试文档

- [单元测试总结](./unit-testing-summary.md) - 测试覆盖情况

## ⚙️ 配置指南

- [Flux高分辨率配置](./FLUX_HIGH_RESOLUTION_CONFIG.md) - Flux工作流高分辨率设置

---

## 🔄 最近更新

### 2025-01-07
- 🎨 **LoRA模型管理工具集成完成**
  - ✅ **自动发现工具**: `discover_lora_models.py` - 自动扫描并发现新LoRA模型
  - ✅ **模型分析工具**: `analyze_lora_models.py` - 智能分析和标注模型用途
  - ✅ **预定义模板**: 8种专业模板，支持一键应用
  - ✅ **启动脚本集成**: 自动发现功能集成到 `start-all-services.sh`
  - 📚 **文档更新**: 
    - [LoRA管理指南](./LORA_MANAGEMENT_GUIDE.md) - 更新v1.2.0，添加完整工作流程
    - [LoRA工具快速参考](./LORA_TOOLS_QUICK_REFERENCE.md) - 新增快速参考文档
  - 🔧 **工具特性**:
    - 支持模拟模式预览
    - 智能触发词匹配
    - 批量模型优化
    - 团队协作最佳实践

### 2025-01-07
- 🎉 **系统重构完成，架构文档更新**
  - ✅ **重构完成**: 统一路由系统整合，消除冗余代码
  - ✅ **功能边界清晰**: 明确各组件职责，提高代码质量
  - ✅ **测试全覆盖**: 44个测试用例全部通过
  - 📚 **新增文档**: 
    - [当前系统架构](./architecture/CURRENT_SYSTEM_ARCHITECTURE.md) - 重构后的完整架构说明
    - [代码文件功能指南](./CODE_FILES_FUNCTION_GUIDE.md) - 详细的代码文件功能说明
    - [系统重构总结](./REFACTORING_SUMMARY.md) - 重构过程、成果和影响
  - 🧹 **文档清理**: 删除过时文档，重新组织文档结构
    - [文档清理总结](./DOCUMENTATION_CLEANUP_SUMMARY.md) - 清理详情和保留原则

### 2024-12-20
- 🚀 **统一路由系统实现完成**
  - 实现了两级路由架构：触发词识别 + LLM智能分析
  - 支持aigen、kontext、kontext_api三种工作流管道
  - 集成了智能提示词优化和参数分析

---

**维护人员**: AI Assistant
**最后更新**: 2025-01-07