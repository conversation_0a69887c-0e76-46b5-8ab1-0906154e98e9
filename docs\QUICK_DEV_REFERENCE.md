# 二次开发快速参考

> 🚀 快速定位二次开发文件的简化索引

## 🎯 核心入口文件

| 功能 | 主要文件 | 位置 |
|------|----------|------|
| **路由系统** | `unified_routing_system.py` | `pkg/core/workflow/` |
| **核心代理** | `comfyui_agent.py` | `pkg/provider/runners/` |
| **Flux工作流** | `flux_workflow_manager.py` | `pkg/workers/flux/` |
| **Kontext工作流** | `kontext_workflow_executor.py` | `pkg/workers/kontext/` |
| **会话管理** | `manager.py` | `pkg/core/session/` |

## 📁 目录功能速查

```
pkg/
├── core/                    # 🏗️ 核心功能
│   ├── workflow/           # 🎯 路由系统
│   ├── session/            # 🗂️ 会话管理  
│   ├── intent/             # 🧠 意图分析
│   ├── image/              # 🖼️ 图像处理
│   └── message/            # 💬 消息处理
├── workers/                 # 🚀 工作流执行器
│   ├── flux/               # 🎨 Flux文生图
│   ├── kontext/            # 🖼️ 图像编辑
│   ├── kontext_api/        # 🌐 云端API
│   └── shared/             # 🔧 共享组件
├── provider/runners/        # 🤖 服务代理
├── adapters/               # 🔌 平台适配
├── pipeline/               # ⚡ 管道集成
├── processors/             # 🔄 消息处理
├── routers/                # 🎯 路由器
└── services/               # 🛠️ 业务服务
```

## 🔧 常用修改场景

### 添加新工作流
1. 在 `pkg/workers/` 下创建新目录
2. 实现工作流管理器和执行器
3. 在 `unified_routing_system.py` 中添加路由规则
4. 更新 `shared_enums.py` 中的枚举

### 修改路由逻辑
- 主文件: `pkg/core/workflow/unified_routing_system.py`
- 配置: `config/unified_routing.yaml`
- 枚举: `pkg/core/workflow/shared_enums.py`

### 增强图像处理
- 核心处理: `pkg/core/image/processor.py`
- 工作流处理: `pkg/workers/kontext/kontext_image_processor.py`
- 标准处理: `pkg/provider/runners/standard_image_handler.py`

### 添加新的AI服务
1. 在 `pkg/provider/runners/` 创建新的代理
2. 继承 `base_agent.py` 基类
3. 在 `comfyui_agent.py` 中集成调用

## 🆕 最近更新 (2025-07-06)

- ✅ **统一路由系统重构**: 大幅简化代码，提升性能
- ✅ **LoRA集成增强**: 支持Civitai自动下载
- ✅ **Kontext精确路由**: 按图片数量精确路由
- ✅ **ComfyUI Agent清理**: 消除冗余代码

## 📋 开发检查清单

### 添加新文件时
- [ ] 选择合适的目录位置
- [ ] 遵循现有命名规范
- [ ] 添加适当的文档字符串
- [ ] 更新 `SECONDARY_DEVELOPMENT_INDEX.md`

### 修改现有文件时
- [ ] 保持向后兼容性
- [ ] 更新相关测试
- [ ] 检查导入关系
- [ ] 更新文档索引的修改日期

### 提交代码时
- [ ] 运行相关测试
- [ ] 检查代码质量
- [ ] 更新commit message
- [ ] 同步更新文档索引

## 🔗 相关文档

- [完整索引](SECONDARY_DEVELOPMENT_INDEX.md) - 详细的文件索引
- [架构文档](architecture/) - 系统架构说明
- [部署指南](deployment/) - 部署和配置
- [贡献指南](CONTRIBUTING.md) - 开发规范

---

> 💡 **提示**: 这是简化版参考，详细信息请查看完整索引文档
