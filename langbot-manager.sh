#!/bin/bash

# LangBot 容器管理脚本
# 解决docker-compose兼容性问题

CONTAINER_NAME="langbot"
IMAGE_NAME="docker.langbot.app/langbot-public/rockchin/langbot:latest"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：停止并删除容器
stop_and_remove() {
    print_info "停止并删除容器..."
    
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        print_info "停止容器 $CONTAINER_NAME"
        docker stop $CONTAINER_NAME
    fi
    
    if docker ps -aq -f name=$CONTAINER_NAME | grep -q .; then
        print_info "删除容器 $CONTAINER_NAME"
        docker rm $CONTAINER_NAME
    fi
}

# 函数：创建并启动容器
create_and_start() {
    print_info "创建并启动容器..."
    
    # 读取docker-compose.yaml中的最新token
    if [ ! -f "docker-compose.yaml" ]; then
        print_error "未找到docker-compose.yaml文件"
        exit 1
    fi
    
    # 提取API_KEY
    API_KEY=$(grep "API_KEY_COMFY_ORG=" docker-compose.yaml | sed 's/.*API_KEY_COMFY_ORG=//' | sed 's/"//g')
    
    if [ -z "$API_KEY" ]; then
        print_error "无法从docker-compose.yaml中提取API_KEY_COMFY_ORG信息"
        exit 1
    fi
    
    print_info "使用最新的ComfyUI API Key创建容器"
    
    docker run -d --name $CONTAINER_NAME \
      --network host \
      --restart on-failure \
      -v ./data:/app/data \
      -v ./plugins:/app/plugins \
      -v ./pkg:/app/pkg \
      -v ./config:/app/config \
      -v ./workflows:/app/workflows \
      -v ./templates:/app/templates \
      -v ./res:/app/res \
      -v ./temp:/app/temp \
      -e TZ=Asia/Shanghai \
      -e "API_KEY_COMFY_ORG=$API_KEY" \
      $IMAGE_NAME
    
    if [ $? -eq 0 ]; then
        print_info "容器创建成功！"
        return 0
    else
        print_error "容器创建失败！"
        return 1
    fi
}

# 函数：查看容器状态
status() {
    print_info "检查容器状态..."
    
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        print_info "容器 $CONTAINER_NAME 正在运行"
        docker ps -f name=$CONTAINER_NAME
    elif docker ps -aq -f name=$CONTAINER_NAME | grep -q .; then
        print_warning "容器 $CONTAINER_NAME 存在但未运行"
        docker ps -a -f name=$CONTAINER_NAME
    else
        print_warning "容器 $CONTAINER_NAME 不存在"
    fi
}

# 函数：查看日志
logs() {
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        print_info "显示容器日志（最近50行）..."
        docker logs $CONTAINER_NAME --tail 50 -f
    else
        print_error "容器 $CONTAINER_NAME 未运行"
    fi
}

# 函数：重启容器（完全重建）
restart() {
    print_info "重启容器（完全重建以加载最新环境变量）..."
    stop_and_remove
    sleep 2
    create_and_start
}

# 函数：检查token状态
check_token() {
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        print_info "检查容器内Firebase token状态..."
        docker exec $CONTAINER_NAME python -c "
import os
import base64
import json
import time
from datetime import datetime

token = os.environ.get('AUTH_TOKEN_COMFY_ORG', '')
if token:
    try:
        parts = token.split('.')
        if len(parts) >= 2:
            payload = parts[1]
            padding = len(payload) % 4
            if padding:
                payload += '=' * (4 - padding)
            
            decoded_bytes = base64.urlsafe_b64decode(payload)
            decoded_json = json.loads(decoded_bytes)
            
            exp = decoded_json.get('exp', 0)
            now = int(time.time())
            
            print(f'Token过期时间: {datetime.fromtimestamp(exp)}')
            print(f'当前时间: {datetime.fromtimestamp(now)}')
            print(f'剩余时间: {exp - now} 秒')
            
            if now < exp:
                print('✅ Token有效')
            else:
                print(f'❌ Token已过期 {now - exp} 秒')
        else:
            print('❌ Token格式错误')
    except Exception as e:
        print(f'解析token失败: {e}')
else:
    print('❌ 未找到token')
"
    else
        print_error "容器 $CONTAINER_NAME 未运行"
    fi
}

# 主函数
main() {
    case "$1" in
        start)
            create_and_start
            ;;
        stop)
            stop_and_remove
            ;;
        restart)
            restart
            ;;
        status)
            status
            ;;
        logs)
            logs
            ;;
        token)
            check_token
            ;;
        *)
            echo "LangBot 容器管理脚本"
            echo ""
            echo "用法: $0 {start|stop|restart|status|logs|token}"
            echo ""
            echo "命令说明:"
            echo "  start   - 创建并启动容器"
            echo "  stop    - 停止并删除容器"
            echo "  restart - 重启容器（完全重建以加载最新环境变量）"
            echo "  status  - 查看容器状态"
            echo "  logs    - 查看容器日志"
            echo "  token   - 检查Firebase token状态"
            echo ""
            echo "注意: 此脚本解决了docker-compose兼容性问题"
            exit 1
            ;;
    esac
}

main "$@" 