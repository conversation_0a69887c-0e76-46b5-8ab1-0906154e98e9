# Lora模型管理指南

## 概述

LangBot现在支持智能的Lora模型管理系统，可以根据用户输入自动选择合适的Lora模型，提升图像生成质量。系统支持本地模型和Civitai远程模型的统一管理。

## 功能特性

### 🎯 智能模型选择
- 根据用户输入的关键词自动匹配最合适的Lora模型
- 支持多种触发词匹配（中文、英文）
- 按评分和下载量智能排序

### 📂 分类管理
- **建筑类 (architecture)**: 专门用于建筑图像生成
- **人像类 (portrait)**: 人像、人物相关
- **风景类 (landscape)**: 自然风景、景观
- **动漫类 (anime)**: 动漫、二次元风格
- **细节增强类 (detail)**: 提升图像细节和质量
- **风格类 (style)**: 各种艺术风格
- **物体类 (object)**: 特定物体或物品
- **其他类 (other)**: 其他类型

### 🌐 Civitai集成
- 支持从Civitai搜索和导入高质量Lora模型
- 自动获取模型评分、下载量等信息
- 远程模型信息管理

## 使用方法

### 1. 查看所有Lora模型
```
/lora list
```

### 2. 按分类查看模型
```
/lora list architecture    # 查看建筑类模型
/lora list detail          # 查看细节增强类模型
/lora list portrait        # 查看人像类模型
```

### 3. 查看模型详细信息
```
/lora info ASTRA_Flux_OC_Vbeta-2
```

### 4. 搜索匹配的模型
```
/lora search 建筑          # 搜索建筑相关模型
/lora search detail        # 搜索细节增强模型
```

### 5. 从Civitai搜索和更新模型信息
```
/lora update flux          # 搜索Flux相关模型
/lora update architecture  # 搜索建筑相关模型
```

### 6. 下载Civitai模型到本地
```
/lora download civitai_12345_awesome_lora  # 下载指定的远程模型
```

### 7. 检查模型状态
```
/lora status detail_aidmafluxproultra-FLUX-v0.1  # 检查模型详细状态
```

### 8. 配置模型属性
```
/lora config detail_aidmafluxproultra-FLUX-v0.1 weight 0.9
/lora config ASTRA_Flux_OC_Vbeta-2 is_active false
```

### 9. 查看统计信息
```
/lora stats
```

## 命令行工具

除了聊天命令外，还提供了多个独立的命令行工具来管理LoRA模型：

### 1. 自动发现工具 (`discover_lora_models.py`)

**用途：** 自动扫描ComfyUI的loras文件夹，发现新的.safetensors文件并添加到配置中

```bash
# 模拟模式 - 查看会发现哪些新模型，但不实际修改配置
python3 discover_lora_models.py --dry-run

# 实际执行 - 发现新模型并自动添加到配置文件
python3 discover_lora_models.py

# 查看帮助
python3 discover_lora_models.py --help
```

**使用场景：**
- 下载了新的LoRA模型后
- 定期检查是否有新模型
- 批量导入模型到系统

### 2. 模型分析工具 (`analyze_lora_models.py`)

**用途：** 手动分析和标注LoRA模型的真实用途，提供准确的分类和触发词

```bash
# 查看可用模板
python3 analyze_lora_models.py templates

# 分析单个模型
python3 analyze_lora_models.py analyze "模型名称"

# 应用预定义模板
python3 analyze_lora_models.py template "模型名称" --template "模板名称" --save

# 自定义更新模型
python3 analyze_lora_models.py update "模型名称" --category "architecture" --trigger-words "建筑,architecture,building" --description "专业建筑渲染模型" --priority --save
```

**可用模板：**
- `建筑渲染` - 专业建筑效果图渲染
- `室内设计` - 室内设计和装饰
- `人像美化` - 人像美化和优化
- `风景摄影` - 自然风景和景观摄影
- `动漫风格` - 动漫和二次元风格
- `细节增强` - 图像细节和质量增强
- `写实风格` - 写实和照片级渲染
- `艺术风格` - 艺术风格和创意渲染

### 3. 状态检查工具 (`check_lora_status.py`)

**用途：** 检查和管理LoRA模型状态，搜索和下载Civitai模型

```bash
# 检查模型状态
python3 check_lora_status.py status <模型名>

# 列出所有模型
python3 check_lora_status.py list

# 搜索Civitai模型
python3 check_lora_status.py search <关键词> --limit 10

# 更新模型信息
python3 check_lora_status.py update <关键词> --limit 20

# 下载模型
python3 check_lora_status.py download <模型名>
```

## 当前配置的模型

### 🏗️ 建筑类模型
- **ASTRA_Flux_OC_Vbeta-2**: ASTRA建筑风格，专门用于建筑类图像
- **flux-lora-NewShikumen**: 上海石库门建筑风格
- **RA_curtain_wall_rank16_bf16**: 现代建筑幕墙
- **比鲁斯商业建筑modern architecture_V0.2**: 现代商业建筑
- **比鲁斯现代酒店Modern Hotel Design_XL1.0-PRO**: 现代酒店设计
- **比鲁斯别墅度假酒店Holiday cottages in Billus_XL1.0_PRO**: 别墅度假酒店
- **加州别墅-1.0**: 加州风格别墅

### 🔍 细节增强类模型
- **detail_aidmafluxproultra-FLUX-v0.1**: 主要细节增强模型（推荐）
- **add-detail-xl**: XL版本细节增强

### 🎨 风格类模型
- **flux_realism_lora**: Flux写实风格
- **比鲁斯F.1中古室内Medieval Ancient Style _V0.1**: 中古室内风格
- **ck-flaani-flux**: Flaani Flux风格
- **flat2100**: 扁平化风格
- **flux1-canny-dev-lora**: Flux Canny边缘检测
- **flux1-depth-dev-lora**: Flux深度检测
- **Flux Turbo Lora_StarAi_Flux Turbo Lora**: Flux Turbo快速生成
- **Hyper-FLUX.1-dev-8steps-lora**: Hyper Flux快速生成

### 👤 人像类模型
- **goodhands_Beta_Gtonero**: 手部细节增强

### 👕 物体类模型
- **outfit-generator**: 服装生成

## 智能选择逻辑

### 1. 触发词匹配
系统会根据用户输入中的关键词自动匹配最合适的Lora模型：

- "建筑" → 建筑类模型
- "细节" → 细节增强类模型
- "写实" → 写实风格模型
- "手" → 手部细节模型

### 2. 默认模型
如果没有找到匹配的模型，系统会自动使用细节增强类模型来提升图像质量。

### 3. 权重配置
每个模型都有默认权重配置，可以在使用时动态调整：

```python
# 示例：调整模型权重
/lora config detail_aidmafluxproultra-FLUX-v0.1 weight 0.9
```

## Civitai集成

### 搜索远程模型
```
/lora update flux          # 搜索Flux相关模型
/lora update architecture  # 搜索建筑相关模型
/lora update portrait      # 搜索人像相关模型
```

### 远程模型特点
- ✅ 自动获取评分和下载量信息
- ✅ 支持模型详情查看
- ✅ 可以下载到本地使用
- ✅ 智能分类和标签识别
- ✅ 支持模型状态检查

### 获取Civitai API Key
1. 访问 [Civitai](https://civitai.com)
2. 注册并登录账户
3. 在设置中生成API Key
4. 配置到系统中（可选）

## 配置文件

Lora模型配置存储在 `config/lora_models.json` 文件中，包含以下信息：

```json
{
  "name": "模型名称",
  "filename": "文件名",
  "file_path": "文件路径",
  "category": "分类",
  "weight": 0.8,
  "trigger_words": ["触发词1", "触发词2"],
  "description": "模型描述",
  "civitai_id": "Civitai ID",
  "civitai_url": "Civitai URL",
  "rating": 4.5,
  "downloads": 1000,
  "is_local": true,
  "is_active": true
}
```

## 完整工作流程

### 场景1：新模型导入
```bash
# 1. 将新LoRA文件放到ComfyUI/models/loras/目录
# 2. 自动发现新模型
python3 discover_lora_models.py

# 3. 分析新模型并标注用途
python3 analyze_lora_models.py analyze "新模型名称"
python3 analyze_lora_models.py template "新模型名称" --template "建筑渲染" --save

# 4. 验证配置
python3 check_lora_status.py list
```

### 场景2：批量优化现有模型
```bash
# 1. 查看所有模型
python3 check_lora_status.py list

# 2. 逐个分析重要模型
python3 analyze_lora_models.py analyze "ASTRA_XL_OC_V3"
python3 analyze_lora_models.py analyze "detail_aidmafluxproultra-FLUX-v0.1"

# 3. 应用合适的模板
python3 analyze_lora_models.py template "ASTRA_XL_OC_V3" --template "建筑渲染" --save
python3 analyze_lora_models.py template "detail_aidmafluxproultra-FLUX-v0.1" --template "细节增强" --save
```

### 场景3：从Civitai获取高质量模型
```bash
# 1. 搜索感兴趣的主题
python3 check_lora_status.py search "architecture"

# 2. 查看搜索结果，选择喜欢的模型
# 3. 下载模型
python3 check_lora_status.py download "civitai_12345_awesome_architecture"

# 4. 自动发现新下载的模型
python3 discover_lora_models.py

# 5. 标注模型用途
python3 analyze_lora_models.py template "新模型名称" --template "建筑渲染" --save
```

## 最佳实践

### 1. 定期维护
```bash
# 每周运行一次，保持模型库更新
python3 discover_lora_models.py --dry-run  # 先检查
python3 discover_lora_models.py            # 确认无误后执行
```

### 2. 质量优先
- 优先标注重要的、常用的模型
- 为细节增强、写实风格等通用模型设置优先级
- 定期清理不用的模型

### 3. 团队协作
- 建立模型命名规范
- 统一使用预定义模板
- 定期review和优化模型配置

### 4. 模型选择建议
- **建筑图像**: 优先使用 `ASTRA_Flux_OC_Vbeta-2`
- **细节增强**: 使用 `detail_aidmafluxproultra-FLUX-v0.1`
- **写实风格**: 使用 `flux_realism_lora`
- **快速生成**: 使用 `Flux Turbo` 或 `Hyper-FLUX` 系列

### 5. 权重调整
- **细节增强**: 0.7-0.9
- **风格模型**: 0.6-0.8
- **建筑模型**: 0.8-1.0

### 6. 组合使用
可以同时使用多个Lora模型：
- 建筑模型 + 细节增强
- 风格模型 + 细节增强
- 人像模型 + 手部细节

## 故障排除

### 1. 模型未找到
- 检查模型文件是否存在
- 确认文件路径正确
- 验证模型配置是否启用

### 2. 效果不理想
- 调整模型权重
- 尝试不同的触发词
- 检查模型分类是否正确

### 3. Civitai连接失败
- 检查网络连接
- 验证API Key是否正确
- 确认Civitai服务状态

## 更新日志

### v1.2.0 (当前版本)
- ✅ 基础Lora模型管理系统
- ✅ 智能模型选择
- ✅ **完整的Civitai API集成**
- ✅ **模型搜索和下载功能**
- ✅ **模型状态检查工具**
- ✅ 分类管理
- ✅ 命令行工具
- ✅ **独立的check_lora_status.py工具**
- ✅ **自动发现工具 (discover_lora_models.py)**
- ✅ **模型分析工具 (analyze_lora_models.py)**
- ✅ **预定义模板系统**
- ✅ **完整工作流程指南**

### 新增功能 (v1.2.0)
- 🆕 **自动发现新LoRA模型**
- 🆕 **智能模型分析和标注**
- 🆕 **8种预定义模板**
- 🆕 **批量模型优化**
- 🆕 **完整的工作流程指南**
- 🆕 **团队协作最佳实践**

### 计划功能
- 🔄 模型效果预览
- 🔄 自动权重优化
- 🔄 模型效果对比
- 🔄 用户自定义分类
- 🔄 模型使用统计
- 🔄 自动备份和恢复

## 技术支持

如果遇到问题，可以：
1. 查看系统日志
2. 使用 `/lora stats` 检查配置状态
3. 使用 `/lora list` 验证模型列表
4. 联系技术支持

---

*最后更新: 2024年12月* 