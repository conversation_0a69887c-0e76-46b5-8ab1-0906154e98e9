#!/usr/bin/env python3
"""
[已废弃] 此文件功能已集成到 check_lora_status.py 中

请使用以下命令替代：
- python3 check_lora_status.py list
- python3 check_lora_status.py status <模型名>
- python3 discover_lora_models.py --dry-run

此文件将被重命名为 .bak 文件
"""

import asyncio
import sys
import json
import aiohttp
from pathlib import Path
from typing import List, Dict, Set

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from pkg.workers.shared.shared_lora_manager import SharedLoraManager


async def get_comfyui_lora_list() -> List[str]:
    """获取ComfyUI中实际可用的LoRA列表"""
    try:
        api_url = "http://127.0.0.1:8188/object_info/LoraLoader"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url) as response:
                if response.status == 200:
                    data = await response.json()
                    lora_list = data.get("LoraLoader", {}).get("input", {}).get("required", {}).get("lora_name", [[], {}])[0]
                    return lora_list
                else:
                    print(f"❌ 获取ComfyUI LoRA列表失败: HTTP {response.status}")
                    return []
    except Exception as e:
        print(f"❌ 查询ComfyUI LoRA列表失败: {e}")
        return []


def check_filename_similarity(name1: str, name2: str) -> bool:
    """检查两个文件名是否相似"""
    import re
    # 移除扩展名进行比较
    base1 = name1.replace('.safetensors', '').replace('.ckpt', '').replace('.pt', '')
    base2 = name2.replace('.safetensors', '').replace('.ckpt', '').replace('.pt', '')
    
    # 标准化（移除特殊字符，转小写）
    base1_clean = re.sub(r'[_\-\s]+', '', base1.lower())
    base2_clean = re.sub(r'[_\-\s]+', '', base2.lower())
    
    return (base1_clean in base2_clean or base2_clean in base1_clean or 
            base1_clean == base2_clean)


async def main():
    print("🔍 检查LoRA配置与ComfyUI同步状态...")

    # 1. 加载配置文件中的LoRA模型
    print("\n📋 加载配置文件...")
    manager = SharedLoraManager()
    manager.initialize()

    config_models = list(manager.lora_models.values())
    print(f"✅ 配置文件中有 {len(config_models)} 个LoRA模型")

    # 显示一些示例配置
    print("\n📝 配置文件示例:")
    for i, model in enumerate(config_models[:3]):
        print(f"   {i+1}. {model.name}")
        print(f"      文件名: {model.filename}")
        print(f"      路径: {model.file_path}")

    # 2. 获取ComfyUI中的LoRA列表
    print("\n🔗 查询ComfyUI可用LoRA...")
    comfyui_loras = await get_comfyui_lora_list()

    if not comfyui_loras:
        print("❌ 无法获取ComfyUI LoRA列表，请确保ComfyUI正在运行")
        print("\n💡 离线分析建议:")
        print("   1. 检查文件路径格式")
        print("   2. 查看子目录结构")
        print("   3. 验证文件名一致性")

        # 离线分析文件路径
        print("\n📁 文件路径分析:")
        root_files = []
        subdir_files = []

        for model in config_models:
            if "/lcm/" in model.file_path or "/SDXL/" in model.file_path:
                subdir_files.append(model)
            else:
                root_files.append(model)

        print(f"   根目录文件: {len(root_files)} 个")
        print(f"   子目录文件: {len(subdir_files)} 个")

        if subdir_files:
            print("\n📂 子目录文件示例:")
            for model in subdir_files[:3]:
                print(f"   - {model.filename} -> {model.file_path}")

        return

    print(f"✅ ComfyUI中有 {len(comfyui_loras)} 个LoRA文件")

    # 显示ComfyUI文件示例
    print("\n📝 ComfyUI文件示例:")
    for i, lora_file in enumerate(comfyui_loras[:5]):
        print(f"   {i+1}. {lora_file}")
    
    # 3. 检查同步状态
    print("\n🔍 检查同步状态...")
    
    exact_matches = []
    similar_matches = []
    missing_files = []
    
    for model in config_models:
        filename = model.filename
        
        # 精确匹配
        if filename in comfyui_loras:
            exact_matches.append(model)
        else:
            # 查找相似文件
            found_similar = False
            for comfyui_file in comfyui_loras:
                if check_filename_similarity(filename, comfyui_file):
                    similar_matches.append((model, comfyui_file))
                    found_similar = True
                    break
            
            if not found_similar:
                missing_files.append(model)
    
    # 4. 显示结果
    print(f"\n📊 同步状态报告:")
    print(f"✅ 精确匹配: {len(exact_matches)} 个")
    print(f"⚠️  相似匹配: {len(similar_matches)} 个")
    print(f"❌ 缺失文件: {len(missing_files)} 个")
    
    # 5. 显示详细信息
    if similar_matches:
        print(f"\n⚠️  需要修正的文件名:")
        for model, correct_file in similar_matches[:10]:  # 只显示前10个
            print(f"   {model.name}:")
            print(f"     配置: {model.filename}")
            print(f"     实际: {correct_file}")
    
    if missing_files:
        print(f"\n❌ 缺失的文件 (前10个):")
        for model in missing_files[:10]:
            print(f"   {model.name}: {model.filename}")
    
    # 6. 提供修复建议
    if similar_matches or missing_files:
        print(f"\n💡 修复建议:")
        print(f"   1. 运行自动发现: python3 discover_lora_models.py")
        print(f"   2. 检查文件路径: python3 check_lora_status.py list")
        print(f"   3. 手动修正配置: python3 analyze_lora_models.py update <模型名> --filename <正确文件名>")
    else:
        print(f"\n🎉 所有LoRA模型都已正确同步！")


if __name__ == "__main__":
    asyncio.run(main())
