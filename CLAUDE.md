# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup
```bash
# Install dependencies using Poetry
poetry install

# Install with dev dependencies
poetry install --with dev

# Alternative: Install using uv (faster)
uv sync
```

### Running the Application
```bash
# Start the application
python main.py

# Start with Docker Compose
docker-compose up -d

# Start all services (WeChat + LangBot)
./start-all-services.sh

# Stop all services
./stop-all-services.sh
```

### Testing
```bash
# Run all tests
poetry run pytest -v

# Run specific test file
poetry run pytest tests/test_specific_file.py -v

# Run with coverage
poetry run pytest --cov=pkg tests/
```

### Code Quality
```bash
# Lint code with ruff
poetry run ruff check pkg/

# Format code with ruff
poetry run ruff format pkg/

# Run pre-commit hooks
poetry run pre-commit run --all-files
```

### Development Scripts
```bash
# Check LLM status
python scripts/check_llm_status.py

# Fix ComfyUI memory issues
python scripts/fix_comfyui_memory_issues.py

# Check development index
python check_dev_index.py
```

## High-Level Architecture

### Core System Design
LangBot is a **multi-agent AI chatbot system** with a **two-level routing architecture**:

1. **Platform Layer**: Abstracts different messaging platforms (WeChat, Telegram, Discord, etc.)
2. **Pipeline Layer**: Standardizes request processing through configurable stages
3. **Routing Layer**: Intelligent request routing using keyword detection + LLM analysis
4. **Workflow Layer**: Specialized AI generation workflows (Flux, Kontext, etc.)
5. **Provider Layer**: Abstracts different AI service providers (OpenAI, DeepSeek, etc.)

### Key Architectural Components

#### Two-Level Routing System (`pkg/core/workflow/`)
- **Level 1**: Fast keyword-based trigger detection (`aigen`, `kontext`, `kontext_api`)
- **Level 2**: LLM-powered intelligent workflow selection and parameter optimization
- **unified_routing_system.py**: Central routing orchestration
- **workflow_parameter_adapter.py**: Parameter conversion between workflow types

#### Message Flow Architecture
```
Platform → Adapter → Query → Pipeline → Runner → Routing → Workflow → Response
```

#### Pipeline Processing (`pkg/pipeline/`)
Sequential processing stages with forking support:
- **PreProc**: Session setup and message normalization
- **ResPRule**: Response rule matching and trigger detection
- **BanSess**: Session access control
- **CntFilter**: Content moderation and filtering
- **Process**: Core processing (chat/command handling)
- **LongText**: Long text handling strategies
- **RespBack**: Response delivery and formatting

#### Specialized Workers (`pkg/workers/`)
- **`flux/`**: Advanced Flux/Stable Diffusion workflows with LoRA integration
- **`kontext/`**: Image editing and manipulation workflows
- **`kontext_api/`**: Cloud-based image processing
- **`shared/`**: Common components (ComfyUI client, LoRA manager, image handlers)

#### Runner System (`pkg/provider/runners/`)
- **`smart_hybrid_agent.py`**: Main request router and system entry point
- **`comfyui_agent.py`**: Workflow execution specialist for image generation
- **`base_agent.py`**: Base runner interface

### Configuration Architecture
- **`service.yaml`**: Core service configuration (ports, LLM, ComfyUI settings)
- **`unified_routing.yaml`**: Routing system configuration
- **`platform.yaml`**: Platform-specific adapter settings
- **`llm_config.yaml`**: LLM provider configurations
- **`wechat_comfyui.yaml`**: WeChat-ComfyUI integration settings

### Platform Support
- **WeChat**: `wechatpad.py`, `gewechat.py` (primary platforms)
- **Enterprise**: `wecom.py`, `dingtalk.py`, `lark.py`
- **Social**: `telegram.py`, `discord.py`, `slack.py`
- **Developer**: `qqofficial.py`, `qqbotpy.py`
- **Web**: `webchat.py` for web-based interactions

### Image Generation Workflows
- **Flux**: Text-to-image generation with LoRA model support
- **Kontext**: Image editing and manipulation (local + API)
- **ComfyUI Integration**: Deep integration with ComfyUI for advanced workflows

## Development Guidelines

### Secondary Development Isolation
This project follows strict isolation principles for custom development:

- **Custom Code Location**: All secondary development goes in dedicated directories
- **Code Marking**: Custom files marked with `# === 二次开发 by blueraincoat ===`
- **Official Code Protection**: Never directly modify official LangBot directories
- **Branch Strategy**: Custom development on `feature/blueraincoat-*` branches

### Key Development Directories
- **`pkg/workers/`**: Custom workflow implementations
- **`pkg/command/operators/`**: Custom command operators
- **`config/`**: Configuration files for custom features
- **`workflows/`**: ComfyUI workflow definitions

### Common Development Tasks

#### Adding New Workflows
1. Create workflow manager in `pkg/workers/[workflow_name]/`
2. Implement workflow models and executors
3. Add routing configuration to `unified_routing.yaml`
4. Create ComfyUI workflow JSON in `workflows/`

#### Adding New Platforms
1. Create platform adapter in `pkg/platform/sources/`
2. Add platform configuration to `platform.yaml`
3. Register platform in bot manager
4. Implement platform-specific message handling

#### Adding New Commands
1. Create command operator in `pkg/command/operators/`
2. Register command in command manager
3. Add command help text and documentation

### Testing Strategy
- **Unit Tests**: Focus on isolated component testing
- **Integration Tests**: Test workflow execution and platform integration
- **Mock External Services**: ComfyUI, LLM providers for reliable testing
- **Test Data**: Use consistent test data in `tests/` directory

### Configuration Management
- **Environment Variables**: Override configuration for different environments
- **Schema Validation**: All configuration files have schema validation
- **Hot Reloading**: Most configuration changes don't require restart
- **Migration System**: Automatic configuration migration on updates

## Important File Locations

### Core Application Files
- **`main.py`**: Application entry point and dependency checking
- **`pkg/app.py`**: Application container and component coordination
- **`pkg/core/boot.py`**: Staged application bootstrapping

### Configuration Files
- **`config/service.yaml`**: Primary service configuration
- **`config/llm_config.yaml`**: LLM provider settings
- **`config/unified_routing.yaml`**: Routing system configuration
- **`pyproject.toml`**: Python project configuration and dependencies

### Documentation
- **`docs/`**: Comprehensive documentation directory
- **`docs/README.md`**: Documentation index
- **`docs/COMFYUI_INTEGRATION.md`**: ComfyUI integration guide
- **`docs/QUICK_DEV_REFERENCE.md`**: Quick development reference

### Docker and Deployment
- **`docker-compose.yaml`**: Main application Docker configuration
- **`wechatpad-docker-compose.yml`**: WeChat adapter Docker configuration
- **`start-all-services.sh`**: Complete system startup script