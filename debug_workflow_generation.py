#!/usr/bin/env python3
"""
调试工作流生成过程
模拟生成过程并保存详细的调试信息
"""

import sys
import json
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from pkg.workers.flux.flux_workflow_manager import FluxWorkflowManager
from pkg.workers.flux.flux_workflow_models import FluxParameters
from pkg.core.entities import WorkflowSession

async def debug_workflow_generation():
    """调试工作流生成过程"""
    print("🔧 开始调试工作流生成过程...")
    
    try:
        # 创建工作流管理器
        manager = FluxWorkflowManager()
        
        # 创建测试参数
        params = FluxParameters(
            prompt="A striking cyberpunk woman stands confidently on a skyscraper rooftop at night",
            negative_prompt="",
            width=1024,
            height=1024,
            steps=20,
            cfg_scale=7.0,
            seed=858218004
        )
        
        # 创建测试会话
        session = WorkflowSession(
            session_id="debug_test",
            user_id="debug_user",
            platform="debug",
            workflow_type="aigen",
            forced_workflow_file="flux_dev_workflow.json"
        )
        
        print(f"📋 测试参数:")
        print(f"   提示词: {params.prompt[:50]}...")
        print(f"   尺寸: {params.width}x{params.height}")
        print(f"   步数: {params.steps}")
        print(f"   种子: {params.seed}")
        
        # 准备工作流
        print(f"\n🔧 准备工作流...")
        workflow_data = await manager.prepare_workflow(params, session, [])
        
        # 保存调试信息
        debug_dir = Path("temp/debug")
        debug_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存完整工作流
        with open(debug_dir / "complete_workflow.json", 'w', encoding='utf-8') as f:
            json.dump(workflow_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 工作流已保存到: {debug_dir / 'complete_workflow.json'}")
        
        # 分析工作流结构
        print(f"\n📊 工作流分析:")
        print(f"   节点总数: {len(workflow_data)}")
        
        # 统计节点类型
        node_types = {}
        lora_nodes = []
        sampler_nodes = []
        output_nodes = []
        
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and "class_type" in node_data:
                class_type = node_data["class_type"]
                node_types[class_type] = node_types.get(class_type, 0) + 1
                
                if "lora" in class_type.lower():
                    lora_nodes.append(node_id)
                elif "sampler" in class_type.lower():
                    sampler_nodes.append(node_id)
                elif "save" in class_type.lower() or "output" in class_type.lower():
                    output_nodes.append(node_id)
        
        print(f"   节点类型统计:")
        for class_type, count in sorted(node_types.items()):
            print(f"     {class_type}: {count}")
        
        print(f"   LoRA节点: {lora_nodes}")
        print(f"   采样器节点: {sampler_nodes}")
        print(f"   输出节点: {output_nodes}")
        
        # 检查关键节点的连接
        print(f"\n🔗 关键节点连接分析:")
        
        # 检查采样器节点
        for sampler_id in sampler_nodes:
            if sampler_id in workflow_data:
                sampler_node = workflow_data[sampler_id]
                inputs = sampler_node.get("inputs", {})
                print(f"   采样器 {sampler_id}:")
                for input_name, input_value in inputs.items():
                    if isinstance(input_value, list) and len(input_value) >= 2:
                        print(f"     {input_name}: 来自节点 {input_value[0]}, 输出 {input_value[1]}")
                    else:
                        print(f"     {input_name}: {input_value}")
        
        # 检查输出节点
        for output_id in output_nodes:
            if output_id in workflow_data:
                output_node = workflow_data[output_id]
                inputs = output_node.get("inputs", {})
                print(f"   输出节点 {output_id}:")
                for input_name, input_value in inputs.items():
                    if isinstance(input_value, list) and len(input_value) >= 2:
                        print(f"     {input_name}: 来自节点 {input_value[0]}, 输出 {input_value[1]}")
                    else:
                        print(f"     {input_name}: {input_value}")
        
        # 检查LoRA链
        if lora_nodes:
            print(f"   LoRA链分析:")
            for lora_id in lora_nodes:
                if lora_id in workflow_data:
                    lora_node = workflow_data[lora_id]
                    inputs = lora_node.get("inputs", {})
                    print(f"     {lora_id}:")
                    for input_name, input_value in inputs.items():
                        if isinstance(input_value, list) and len(input_value) >= 2:
                            print(f"       {input_name}: 来自节点 {input_value[0]}, 输出 {input_value[1]}")
                        else:
                            print(f"       {input_name}: {input_value}")
        
        # 验证连接完整性
        print(f"\n✅ 连接完整性验证:")
        
        # 检查是否有断开的连接
        all_node_ids = set(workflow_data.keys())
        broken_connections = []
        
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and "inputs" in node_data:
                inputs = node_data["inputs"]
                for input_name, input_value in inputs.items():
                    if isinstance(input_value, list) and len(input_value) >= 2:
                        source_node_id = input_value[0]
                        if source_node_id not in all_node_ids:
                            broken_connections.append({
                                "target_node": node_id,
                                "input_name": input_name,
                                "missing_source": source_node_id
                            })
        
        if broken_connections:
            print(f"❌ 发现 {len(broken_connections)} 个断开的连接:")
            for conn in broken_connections:
                print(f"   节点 {conn['target_node']} 的输入 {conn['input_name']} 引用了不存在的节点 {conn['missing_source']}")
        else:
            print(f"✅ 所有连接都完整")
        
        # 保存分析结果
        analysis_result = {
            "node_count": len(workflow_data),
            "node_types": node_types,
            "lora_nodes": lora_nodes,
            "sampler_nodes": sampler_nodes,
            "output_nodes": output_nodes,
            "broken_connections": broken_connections
        }
        
        with open(debug_dir / "workflow_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 分析结果已保存到: {debug_dir / 'workflow_analysis.json'}")
        
        return len(broken_connections) == 0
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import asyncio
    success = asyncio.run(debug_workflow_generation())
    print(f"\n🎉 调试结果: {'✅ 成功' if success else '❌ 失败'}")
