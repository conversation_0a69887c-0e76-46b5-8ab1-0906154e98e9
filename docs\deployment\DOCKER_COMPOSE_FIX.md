# Docker Compose 兼容性问题解决方案

## 问题描述

当前系统中的docker-compose版本(1.29.2)与Docker版本(27.5.1)存在兼容性问题，导致以下错误：

```
docker.errors.DockerException: Error while fetching server API version: Not supported URL scheme http+docker
```

## 问题原因

这是由于新版Docker与旧版docker-compose之间的API兼容性问题导致的。新版Docker使用了不同的API端点格式，而旧版docker-compose无法正确解析。

## 解决方案

我们提供了一个替代脚本 `langbot-manager.sh` 来管理LangBot容器，避免依赖有问题的docker-compose。

### 使用方法

```bash
# 查看脚本帮助
./langbot-manager.sh

# 启动容器
./langbot-manager.sh start

# 停止容器
./langbot-manager.sh stop

# 重启容器（完全重建，加载最新环境变量）
./langbot-manager.sh restart

# 查看容器状态
./langbot-manager.sh status

# 查看容器日志
./langbot-manager.sh logs

# 检查Firebase token状态
./langbot-manager.sh token
```

### 脚本特性

1. **自动提取token**：从docker-compose.yaml中自动提取最新的Firebase token
2. **完全重建**：restart命令会完全删除并重新创建容器，确保环境变量更新
3. **状态检查**：可以检查容器状态和Firebase token有效性
4. **颜色输出**：使用不同颜色显示信息、警告和错误

### 更新Firebase Token流程

1. 更新docker-compose.yaml中的token
2. 运行 `./langbot-manager.sh restart` 重启容器
3. 运行 `./langbot-manager.sh token` 验证token是否有效

## 长期解决方案

### 方案1：升级到Docker Compose V2（推荐）

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install docker-compose-plugin

# 使用新的命令格式
docker compose up -d
docker compose down
```

### 方案2：手动安装Docker Compose V2

```bash
# 下载最新版本
sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose-v2

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose-v2

# 创建别名
sudo ln -sf /usr/local/bin/docker-compose-v2 /usr/local/bin/docker-compose
```

### 方案3：使用Docker内置的compose命令

新版Docker内置了compose功能，可以直接使用：

```bash
# 替代 docker-compose up -d
docker compose up -d

# 替代 docker-compose down
docker compose down

# 替代 docker-compose restart
docker compose restart
```

## 注意事项

1. **环境变量更新**：当更新Firebase token时，必须完全重建容器才能生效
2. **备份数据**：在执行容器操作前，确保重要数据已备份
3. **网络配置**：脚本使用host网络模式，确保与docker-compose.yaml配置一致

## 故障排除

### 如果容器无法启动

1. 检查docker-compose.yaml中的token格式是否正确
2. 确保所有挂载的目录存在
3. 检查端口是否被占用

### 如果token认证失败

1. 运行 `./langbot-manager.sh token` 检查token状态
2. 如果token过期，更新docker-compose.yaml中的token
3. 运行 `./langbot-manager.sh restart` 重启容器

### 如果Kontext工作流失败

1. 确保Firebase token有效且未过期
2. 检查ComfyUI账户是否有足够积分
3. 查看详细日志：`./langbot-manager.sh logs` 