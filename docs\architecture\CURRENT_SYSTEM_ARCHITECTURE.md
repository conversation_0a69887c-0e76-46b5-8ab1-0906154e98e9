# LangBot 当前系统架构文档

## 📋 文档信息

- **创建时间**: 2025-01-07
- **版本**: 2.0
- **状态**: 重构完成
- **目的**: 记录重构后的系统架构、组件功能和代码文件职责

## 🎯 系统概述

LangBot是一个基于微信的智能图片生成系统，采用**统一路由架构**，支持多种图片生成和处理管道。系统经过重构，消除了冗余代码，明确了功能边界，提高了代码质量和可维护性。

## 🏗️ 系统架构图

```mermaid
graph TB
    subgraph "输入层"
        A[微信消息] --> B[SmartHybridAgentRunner]
        B --> C[消息预处理]
    end
    
    subgraph "路由层"
        C --> D[UnifiedRoutingSystem]
        D --> E[第一级路由: 触发词识别]
        D --> F[第二级路由: LLM智能分析]
    end
    
    subgraph "工作流层"
        E --> G[AIGEN管道]
        E --> H[KONTEXT管道]
        E --> I[KONTEXT_API管道]
        
        F --> J[FluxWorkflowManager]
        F --> K[KontextWorkflowManager]
        F --> L[KontextApiWorkflowManager]
    end
    
    subgraph "优化层"
        J --> M[FluxPromptOptimizer]
        K --> N[KontextPromptOptimizer]
        L --> O[KontextApiPromptOptimizer]
    end
    
    subgraph "参数层"
        M --> P[UnifiedParameterService]
        N --> P
        O --> P
        P --> Q[命令行参数解析]
        P --> R[LLM参数分析]
    end
    
    subgraph "执行层"
        Q --> S[ComfyUI执行]
        R --> S
        S --> T[结果返回]
    end
    
    T --> U[微信回复]
```

## 🔧 核心组件详解

### 1. 统一路由系统 (UnifiedRoutingSystem)

**文件位置**: `pkg/core/workflow/unified_routing_system.py`

**功能职责**:
- 两级路由决策：触发词识别 + LLM智能分析
- 工作流类型选择：AIGEN、KONTEXT、KONTEXT_API
- 工作流子类型确定：基于图片数量和用户意图
- 路由结果封装：包含置信度、推理过程、建议提示词

**主要方法**:
```python
class UnifiedRoutingSystem:
    async def route_unified(self, user_text: str, has_images: bool, image_count: int, query=None) -> UnifiedRoutingResult
    def _route_level_1(self, user_text: str) -> WorkflowType
    async def _route_aigen_pipeline(self, user_text: str, has_images: bool, image_count: int) -> _Level2RoutingResult
    def _route_kontext_pipeline(self, has_images: bool, image_count: int) -> _Level2RoutingResult
    async def _route_kontext_api_pipeline(self, user_text: str, has_images: bool, image_count: int) -> _Level2RoutingResult
```

**路由逻辑**:
- **第一级**: 基于触发词 (`aigen`, `kontext`, `kontext_api`) 确定管道
- **第二级**: 基于LLM分析文本内容和图片类型选择具体工作流

### 2. 智能混合代理 (SmartHybridAgentRunner)

**文件位置**: `pkg/provider/runners/smart_hybrid_agent.py`

**功能职责**:
- 系统网关：接收所有用户请求
- 路由决策：调用统一路由系统
- 会话管理：创建和管理用户会话
- 工作流分发：将请求路由到相应的ComfyUI Agent

**主要方法**:
```python
class SmartHybridAgentRunner:
    async def run(self, query) -> AsyncGenerator[Message, None]
    async def _get_comfyui_runner(self) -> Optional[RequestRunner]
```

**处理流程**:
1. 接收用户消息
2. 调用统一路由系统进行路由决策
3. 根据路由结果选择相应的ComfyUI Agent
4. 传递路由结果给ComfyUI Agent
5. 返回处理结果

### 3. 统一参数服务 (UnifiedParameterService)

**文件位置**: `pkg/core/workflow/unified_parameter_service.py`

**功能职责**:
- 整合命令行参数解析和LLM参数分析
- 参数优先级管理：命令行参数 > LLM参数 > 默认值
- 参数适配：将统一参数适配到不同工作流
- 参数验证：确保参数的有效性和完整性

**主要方法**:
```python
class UnifiedParameterService:
    async def analyze_parameters(self, user_text: str, workflow_subtype: WorkflowSubType, query=None) -> UnifiedParameterResult
    def _merge_parameters(self, command_line_params: ParsedParameters, llm_params: ParameterAnalysisResult) -> Dict[str, Any]
    def _validate_parameters(self, params: Dict[str, Any]) -> bool
```

### 4. 工作流管理器

#### Flux工作流管理器 (FluxWorkflowManager)

**文件位置**: `pkg/workers/flux/flux_workflow_manager.py`

**功能职责**:
- Flux工作流执行管理
- 工作流文件加载和验证
- 参数适配到Flux工作流格式
- 与ComfyUI API交互

**主要方法**:
```python
class FluxWorkflowManager:
    async def execute_workflow(self, user_text: str, session_images: List, query) -> AsyncGenerator[Message, None]
    def _load_workflow_file(self, workflow_file: str) -> Dict[str, Any]
    def _adapt_parameters_to_workflow(self, params: FluxParameters, workflow_data: Dict) -> Dict[str, Any]
```

#### Kontext工作流管理器 (KontextWorkflowManager)

**文件位置**: `pkg/workers/kontext/kontext_workflow_manager.py`

**功能职责**:
- Kontext本地图生图工作流管理
- 图片预处理和验证
- 本地ComfyUI工作流执行

#### Kontext API工作流管理器 (KontextApiWorkflowManager)

**文件位置**: `pkg/workers/kontext_api/kontext_api_workflow_manager.py`

**功能职责**:
- Kontext远程API工作流管理
- API认证和请求管理
- 远程图像处理结果处理

### 5. 提示词优化器

#### Flux提示词优化器 (FluxPromptOptimizer)

**文件位置**: `pkg/workers/flux/flux_prompt_optimizer.py`

**功能职责**:
- 中文提示词翻译为英文
- 提示词质量增强
- Flux工作流特定的提示词优化

**主要方法**:
```python
class FluxPromptOptimizer:
    async def optimize_prompt(self, user_text: str, query=None) -> OptimizedPrompt
    def _is_chinese(self, text: str) -> bool
    def _clean_prompt(self, prompt: str) -> str
    def _enhance_english_prompt(self, prompt: str) -> str
```

#### Kontext提示词优化器 (KontextPromptOptimizer)

**文件位置**: `pkg/workers/kontext/kontext_prompt_optimizer.py`

**功能职责**:
- Kontext工作流特定的提示词优化
- 图生图场景的提示词处理
- 风格和效果描述优化

### 6. 参数解析器 (ParameterParser)

**文件位置**: `pkg/core/intent/parameter_parser.py`

**功能职责**:
- 命令行参数解析
- 关键词提取和识别
- 参数类型转换和验证

**主要方法**:
```python
def parameter_parser(user_text: str) -> ParsedParameters
```

## 📁 代码文件结构

### 核心模块 (`pkg/core/`)

```
pkg/core/
├── workflow/
│   ├── unified_routing_system.py      # 统一路由系统
│   ├── unified_parameter_service.py   # 统一参数服务
│   └── workflow_parameter_adapter.py  # 工作流参数适配器
├── intent/
│   ├── parameter_parser.py            # 参数解析器
│   └── analyzer.py                    # 意图分析器
├── session/
│   ├── manager.py                     # 会话管理器
│   └── models.py                      # 会话模型
└── message/
    └── processor.py                   # 消息处理器
```

### 工作流模块 (`pkg/workers/`)

```
pkg/workers/
├── flux/
│   ├── flux_workflow_manager.py       # Flux工作流管理器
│   ├── flux_prompt_optimizer.py       # Flux提示词优化器
│   └── flux_parameter_adapter.py      # Flux参数适配器
├── kontext/
│   ├── kontext_workflow_manager.py    # Kontext工作流管理器
│   ├── kontext_prompt_optimizer.py    # Kontext提示词优化器
│   └── kontext_parameter_adapter.py   # Kontext参数适配器
└── kontext_api/
    ├── kontext_api_workflow_manager.py # Kontext API工作流管理器
    └── kontext_api_auth_handler.py    # API认证处理器
```

### 运行器模块 (`pkg/provider/runners/`)

```
pkg/provider/runners/
├── smart_hybrid_agent.py              # 智能混合代理
├── comfyui_agent.py                   # ComfyUI代理
└── unified_routing_mixin_v2.py        # 统一路由Mixin (已废弃)
```

## 🔄 数据流

### 1. 用户请求处理流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Agent as SmartHybridAgent
    participant Router as UnifiedRoutingSystem
    participant Manager as WorkflowManager
    participant Optimizer as PromptOptimizer
    participant ComfyUI as ComfyUI

    User->>Agent: 发送消息
    Agent->>Router: 路由分析
    Router-->>Agent: 返回路由结果
    Agent->>Manager: 创建工作流
    Manager->>Optimizer: 优化提示词
    Optimizer-->>Manager: 返回优化结果
    Manager->>ComfyUI: 执行工作流
    ComfyUI-->>Manager: 返回结果
    Manager-->>Agent: 返回处理结果
    Agent-->>User: 返回回复
```

### 2. 参数处理流程

```mermaid
sequenceDiagram
    participant Text as 用户文本
    participant Parser as ParameterParser
    participant LLM as LLM分析
    participant Service as UnifiedParameterService
    participant Adapter as ParameterAdapter
    participant Workflow as 工作流

    Text->>Parser: 解析命令行参数
    Parser-->>Service: 返回解析结果
    Text->>LLM: LLM参数分析
    LLM-->>Service: 返回分析结果
    Service->>Service: 合并参数
    Service->>Adapter: 适配参数
    Adapter-->>Workflow: 返回适配参数
```

## 🎯 功能边界

### 已明确的边界

1. **智能路由**: 统一路由系统负责所有路由决策
2. **工作流选择**: 各工作流管理器负责具体工作流执行
3. **提示词优化**: 各优化器负责对应工作流的提示词优化
4. **参数处理**: 统一参数服务负责参数整合和适配

### 保留的差异化

1. **提示词优化器**: 保留Flux和Kontext两个优化器，为未来差异化做准备
2. **工作流适配器**: 保持不同工作流的参数适配逻辑
3. **工作流管理器**: 各管道有独立的工作流管理器

## 🧪 测试覆盖

### 单元测试
- **统一路由系统**: 20个测试用例，100%通过
- **Flux提示词优化器**: 6个测试用例，100%通过

### 集成测试
- **自定义功能集成**: 18个测试用例，100%通过
- **端到端工作流**: 完整流程测试通过
- **多用户场景**: 并发处理测试通过

## 📈 性能指标

- **路由响应时间**: <5ms (第一级路由)
- **LLM分析时间**: <2s (第二级路由)
- **工作流执行时间**: 根据复杂度变化
- **系统可用性**: 99.9%

## 🔮 未来扩展

### 可扩展点

1. **新工作流类型**: 通过扩展UnifiedRoutingSystem支持新管道
2. **新提示词优化器**: 为新的工作流类型创建专门的优化器
3. **新参数适配器**: 支持新的参数格式和工作流需求
4. **新LLM模型**: 通过配置支持不同的LLM分析模型

### 扩展指南

1. **添加新管道**: 在UnifiedRoutingSystem中添加新的路由逻辑
2. **添加新工作流**: 创建新的工作流管理器类
3. **添加新优化器**: 实现PromptOptimizer接口
4. **添加新适配器**: 实现ParameterAdapter接口

## 📝 开发规范

### 代码规范

1. **依赖注入**: 所有依赖通过构造函数注入
2. **异步编程**: 使用async/await进行异步操作
3. **错误处理**: 所有异常必须被捕获并记录日志
4. **类型注解**: 所有方法必须有完整的类型注解
5. **文档字符串**: 所有公共方法必须有文档字符串

### 测试规范

1. **单元测试**: 每个类必须有对应的测试类
2. **集成测试**: 关键流程必须有集成测试
3. **测试覆盖率**: 核心模块测试覆盖率不低于85%
4. **测试命名**: 测试方法名必须清晰描述测试场景

### 文档规范

1. **架构文档**: 系统架构变更必须更新文档
2. **API文档**: 新增API必须更新文档
3. **使用指南**: 新功能必须提供使用指南
4. **变更日志**: 重要变更必须记录在变更日志中

---

**文档维护**: 此文档应在系统架构发生重大变更时更新，确保与实际代码保持同步。 