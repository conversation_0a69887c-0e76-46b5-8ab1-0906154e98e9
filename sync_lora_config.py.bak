#!/usr/bin/env python3
"""
[已废弃] 此文件功能应该集成到现有工具中

建议使用以下现有工具替代：
1. python3 discover_lora_models.py - 重新发现LoRA模型
2. python3 check_lora_status.py list - 检查模型状态
3. python3 analyze_lora_models.py - 分析和修正模型

此文件将被重命名为 .bak 文件
"""

import asyncio
import sys
import json
import aiohttp
from pathlib import Path
from typing import List, Dict, Set

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from pkg.workers.shared.shared_lora_manager import SharedLoraManager


async def get_comfyui_lora_list() -> List[str]:
    """获取ComfyUI中实际可用的LoRA列表"""
    try:
        api_url = "http://127.0.0.1:8188/object_info/LoraLoader"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url) as response:
                if response.status == 200:
                    data = await response.json()
                    lora_list = data.get("LoraLoader", {}).get("input", {}).get("required", {}).get("lora_name", [[], {}])[0]
                    return lora_list
                else:
                    print(f"❌ 获取ComfyUI LoRA列表失败: HTTP {response.status}")
                    return []
    except Exception as e:
        print(f"❌ 查询ComfyUI LoRA列表失败: {e}")
        return []


def backup_config():
    """备份当前配置文件"""
    config_file = "config/lora_models.json"
    backup_file = f"config/lora_models_backup_{int(asyncio.get_event_loop().time())}.json"
    
    if Path(config_file).exists():
        import shutil
        shutil.copy2(config_file, backup_file)
        print(f"✅ 配置文件已备份到: {backup_file}")
        return backup_file
    return None


def clean_config(comfyui_loras: List[str]):
    """清理配置文件，移除不存在的LoRA"""
    config_file = "config/lora_models.json"
    
    if not Path(config_file).exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return
    
    # 读取配置
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    original_count = len(config["models"])
    
    # 过滤存在的模型
    valid_models = []
    removed_models = []
    
    for model in config["models"]:
        filename = model["filename"]
        if filename in comfyui_loras:
            valid_models.append(model)
        else:
            removed_models.append(model)
    
    # 更新配置
    config["models"] = valid_models
    
    # 保存清理后的配置
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 配置清理完成:")
    print(f"   原始模型数: {original_count}")
    print(f"   保留模型数: {len(valid_models)}")
    print(f"   移除模型数: {len(removed_models)}")
    
    if removed_models:
        print(f"\n🗑️ 移除的模型:")
        for model in removed_models[:10]:  # 只显示前10个
            print(f"   - {model['name']}: {model['filename']}")
        if len(removed_models) > 10:
            print(f"   ... 还有 {len(removed_models) - 10} 个")


async def main():
    print("🔄 开始同步LoRA配置...")
    
    # 1. 获取ComfyUI可用LoRA
    print("\n🔗 查询ComfyUI可用LoRA...")
    comfyui_loras = await get_comfyui_lora_list()
    
    if not comfyui_loras:
        print("❌ 无法获取ComfyUI LoRA列表，请确保ComfyUI正在运行")
        return
    
    print(f"✅ ComfyUI中有 {len(comfyui_loras)} 个LoRA文件")
    
    # 2. 备份配置文件
    print("\n💾 备份配置文件...")
    backup_file = backup_config()
    
    # 3. 清理配置文件
    print("\n🧹 清理配置文件...")
    clean_config(comfyui_loras)
    
    # 4. 重新初始化管理器
    print("\n🔄 重新加载配置...")
    manager = SharedLoraManager()
    manager._initialized = False  # 强制重新初始化
    manager.initialize()
    
    print(f"✅ 配置同步完成！当前有 {len(manager.lora_models)} 个有效LoRA模型")
    
    # 5. 建议运行自动发现
    print(f"\n💡 建议:")
    print(f"   1. 运行自动发现添加新模型: python3 discover_lora_models.py")
    print(f"   2. 重启LangBot以加载新配置")
    if backup_file:
        print(f"   3. 如需恢复，使用备份文件: {backup_file}")


if __name__ == "__main__":
    asyncio.run(main())
