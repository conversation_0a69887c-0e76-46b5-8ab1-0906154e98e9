"""
[二次开发] 重构版 KontextImageHandler
只负责流程调度，核心功能委托子模块

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Kontext图片处理器，负责流程调度
- 维护者：开发团队
- 最后更新：2025-01-09
- 相关任务：Kontext图片生成工作流

[迁移说明] 此文件已从 pkg/provider/runners/ 迁移到 pkg/workers/shared/image_handlers/
原因：优化架构层级，将工具类与运行器分离
迁移时间：2025-01-09
"""


from pkg.workers.kontext.kontext_session_manager import KontextSessionManager
from pkg.workers.kontext.kontext_prompt_optimizer import KontextPromptOptimizer
from pkg.workers.kontext.kontext_image_processor import KontextImageProcessor
from pkg.workers.kontext.kontext_workflow_executor import KontextWorkflowExecutor
from pkg.workers.kontext.kontext_comfyui_auth import KontextComfyUIAuth
from pkg.workers.kontext.local_kontext_workflow_manager import LocalKontextWorkflowManager
from pkg.workers.kontext.aspect_optimizer import AspectRatioOptimizer
from pkg.workers.kontext.prompt_upsampler import PromptUpsampler
from typing import List, Any, Dict, Optional, AsyncGenerator
import asyncio
from ....provider import entities as llm_entities

class KontextImageHandler:
    def __init__(self, ap, pipeline_config):
        self.ap = ap
        self.pipeline_config = pipeline_config
        self.session_mgr = KontextSessionManager()
        self.prompt_opt = KontextPromptOptimizer()
        self.img_proc = KontextImageProcessor()
        self.auth = KontextComfyUIAuth()
        
        # 初始化新的模块
        self.workflow_manager = LocalKontextWorkflowManager()
        self.aspect_optimizer = AspectRatioOptimizer()
        self.prompt_upsampler = PromptUpsampler()

    def _determine_mode_from_message(self, user_text: str) -> str:
        """
        根据用户消息确定执行模式
        规则：
        - kontext api xxx → API模式
        - kontext xxx → 本地模式（默认）
        """
        if not user_text:
            return 'local'  # 默认本地模式
        
        text_lower = user_text.lower().strip()
        
        # 检查是否明确指定API模式
        if text_lower.startswith('kontext api '):
            self.ap.logger.info("用户明确指定使用API模式")
            return 'api'
        
        # 其他情况使用本地模式
        self.ap.logger.info("使用默认本地模式")
        return 'local'

    def _extract_clean_prompt(self, user_text: str) -> str:
        """
        提取清理后的提示词（移除模式指令）
        """
        if not user_text:
            return ""
        
        text_lower = user_text.lower().strip()
        
        # 移除 "kontext api " 前缀
        if text_lower.startswith('kontext api '):
            return user_text[12:].strip()  # 移除 "kontext api "
        
        # 移除 "kontext " 前缀
        if text_lower.startswith('kontext '):
            return user_text[8:].strip()  # 移除 "kontext "
        
        return user_text.strip()

    async def handle_kontext_workflow(self, user_text: str, user_images: List[bytes], user_id: str, chat_id: str, query: Any):
        # 1. 确定执行模式
        mode = self._determine_mode_from_message(user_text)
        
        # 2. 提取清理后的提示词
        clean_prompt = self._extract_clean_prompt(user_text)
        
        # 3. 会话管理
        session = self.session_mgr.get_user_session(user_id, chat_id)
        if not session:
            session = self.session_mgr.create_session(user_id, chat_id)
        
        # 4. 提示词优化
        try:
            self.ap.logger.info(f"🔍 [DEBUG] 开始提示词优化:")
            self.ap.logger.info(f"🔍 [DEBUG] - 原始提示词: {clean_prompt}")
            prompt = self.prompt_opt.optimize_prompt(clean_prompt)
            self.ap.logger.info(f"🔍 [DEBUG] - 优化后提示词: {prompt}")
            self.ap.logger.info(f"提示词优化完成: {prompt}")
        except Exception as e:
            self.ap.logger.error(f"提示词优化失败: {e}")
            self.ap.logger.error(f"🔍 [DEBUG] 提示词优化异常，使用原始提示词: {clean_prompt}")
            prompt = clean_prompt  # 使用原始提示词
        
        # 5. 图片处理
        self.ap.logger.info(f"开始验证 {len(user_images)} 张用户图片")
        images = []
        for i, img in enumerate(user_images):
            is_valid = self.img_proc.is_valid_image(img)
            self.ap.logger.info(f"图片 {i+1}: 大小={len(img)} bytes, 有效={is_valid}")
            if is_valid:
                images.append(img)
            else:
                # 显示图片头部信息用于调试
                header = img[:20].hex() if len(img) >= 20 else img.hex()
                self.ap.logger.warning(f"图片 {i+1} 验证失败，头部: {header}")

        self.ap.logger.info(f"图片验证完成: {len(images)}/{len(user_images)} 张有效")
        
        # 6. 选择工作流
        try:
            workflow_config = self.workflow_manager.select_workflow(len(images))
            self.ap.logger.info(f"选择Kontext工作流: {workflow_config.description}")
        except Exception as e:
            self.ap.logger.error(f"选择工作流失败: {e}")
            yield llm_entities.Message(
                role='assistant',
                content=f"选择工作流失败: {str(e)}"
            )
            return
        
        # 7. 确定最佳比例
        try:
            aspect_ratio = self.aspect_optimizer.determine_optimal_ratio(images, clean_prompt)
            self.ap.logger.info(f"确定比例: {aspect_ratio}")
        except Exception as e:
            self.ap.logger.error(f"确定比例失败: {e}")
            aspect_ratio = "3:2"  # 使用默认比例
        
        # 8. 优化生成参数
        try:
            generation_params = self.prompt_upsampler.optimize_generation_params(clean_prompt)
            self.ap.logger.info(f"优化参数: guidance={generation_params.get('guidance')}, steps={generation_params.get('steps')}")
        except Exception as e:
            self.ap.logger.error(f"优化参数失败: {e}")
            generation_params = {'guidance': 4.0, 'steps': 60, 'prompt_upsampling': False}
        
        # 9. 获取ComfyUI配置
        comfyui_config = self.pipeline_config.get('ai', {}).get('comfyui-agent', {})
        api_url = comfyui_config.get('api-url', 'http://localhost:8188')
        timeout = comfyui_config.get('timeout', 180)
        
        # 10. 动态创建执行器
        workflow_exec = KontextWorkflowExecutor(mode=mode, api_url=api_url, timeout=timeout)
        
        try:
            # 11. 工作流组装与执行
            workflow_data = await workflow_exec.prepare_workflow_data(
                {}, images, prompt, aspect_ratio, generation_params
            )

            # 🔍 增加调试信息
            self.ap.logger.info(f"🔍 [DEBUG] 工作流数据准备完成:")
            self.ap.logger.info(f"🔍 [DEBUG] - 提示词: {workflow_data.get('prompt', 'N/A')}")
            self.ap.logger.info(f"🔍 [DEBUG] - 宽高比: {workflow_data.get('aspect_ratio', 'N/A')}")
            self.ap.logger.info(f"🔍 [DEBUG] - 引导值: {workflow_data.get('guidance', 'N/A')}")
            self.ap.logger.info(f"🔍 [DEBUG] - 步数: {workflow_data.get('steps', 'N/A')}")
            self.ap.logger.info(f"🔍 [DEBUG] - 种子: {workflow_data.get('seed', 'N/A')}")
            images_list = workflow_data.get('images', [])
            self.ap.logger.info(f"🔍 [DEBUG] - 图片数量: {len(images_list)}")
            if images_list:
                for i, img in enumerate(images_list):
                    self.ap.logger.info(f"🔍 [DEBUG] - 图片{i+1}: {len(img)} bytes")

            self.ap.logger.info(f"执行Kontext工作流: {workflow_config.workflow_file}")
            result = await workflow_exec.execute_workflow(workflow_config.workflow_file, workflow_data)

            # 🔍 增加执行结果调试信息
            self.ap.logger.info(f"🔍 [DEBUG] 工作流执行结果:")
            self.ap.logger.info(f"🔍 [DEBUG] - 成功: {result.success}")
            self.ap.logger.info(f"🔍 [DEBUG] - 错误信息: {result.error_message}")
            self.ap.logger.info(f"🔍 [DEBUG] - 图片数据: {'有' if result.image_data else '无'}")
            if result.metadata:
                self.ap.logger.info(f"🔍 [DEBUG] - 元数据: {result.metadata}")

            # 12. 记录模式选择
            self.ap.logger.info(f"Kontext工作流执行完成 - 模式: {mode}, 提示词: {clean_prompt}")

            # 13. 处理结果并yield消息
            if result.success:
                if result.image_data:
                    import base64
                    image_base64 = base64.b64encode(result.image_data).decode('utf-8')
                    image_content = llm_entities.ContentElement.from_image_base64(image_base64)
                    yield llm_entities.Message(role='assistant', content=[image_content])
                    yield llm_entities.Message(role='assistant', content="✅ Kontext工作流执行成功，图片已生成")
                else:
                    yield llm_entities.Message(role='assistant', content="✅ Kontext工作流执行成功")
            else:
                yield llm_entities.Message(role='assistant', content=f"❌ Kontext工作流失败: {result.error_message}")
            
        except Exception as e:
            self.ap.logger.error(f"执行Kontext工作流失败: {e}")
            yield llm_entities.Message(
                role='assistant',
                content=f"❌ 执行工作流失败: {str(e)}"
            )
        finally:
            # 14. 关闭执行器
            await workflow_exec.close()

    async def handle_kontext_api_workflow(self, user_text: str, user_images: List[bytes], user_id: str, chat_id: str, query: Any):
        """处理 Kontext API 工作流"""
        try:
            self.ap.logger.info(f"开始执行 Kontext API 工作流")

            # 使用与本地工作流相同的逻辑，但可能需要不同的执行器
            async for message in self.handle_kontext_workflow(user_text, user_images, user_id, chat_id, query):
                yield message

        except Exception as e:
            self.ap.logger.error(f"Kontext API 工作流执行失败: {e}")
            yield llm_entities.Message(
                role='assistant',
                content=f"❌ Kontext API 工作流执行失败: {str(e)}"
            )

    async def handle_kontext_session_interaction(self, user_text: str, user_images: List[bytes], session: Any, user_id: str, chat_id: str, query: Any):
        async for message in self.handle_kontext_workflow(user_text, user_images, user_id, chat_id, query):
            yield message

    def extract_user_images(self, message: Any) -> List[bytes]:
        return self.img_proc.extract_user_images(message)

    def optimize_prompt(self, prompt: str) -> str:
        return self.prompt_opt.optimize_prompt(prompt)

    def is_authenticated(self) -> bool:
        return self.auth.is_authenticated()

    async def send_image_to_wechat(self, image_data: bytes, query: Any) -> bool:
        """发送图片到微信"""
        import tempfile
        import os
        from pkg.platform.types import message as platform_types

        temp_path = None
        try:
            self.ap.logger.info(f"开始发送图片到微信，图片大小: {len(image_data)} bytes")

            # 保存图片到临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(image_data)
                temp_path = temp_file.name

            self.ap.logger.info(f"图片已保存到临时文件: {temp_path}")

            # 创建微信图片消息
            image_message = platform_types.message.Image(path=temp_path)
            message_chain = platform_types.message.MessageChain([image_message])

            self.ap.logger.info(f"开始发送图片消息到微信...")

            # 发送消息
            await query.adapter.reply_message(
                message_source=query.message_event,
                message=message_chain,
                quote_origin=False
            )

            self.ap.logger.info("✅ 图片已成功发送到微信")
            return True

        except Exception as e:
            self.ap.logger.error(f"发送图片到微信失败: {e}")
            return False
        finally:
            # 清理临时文件
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                    self.ap.logger.info(f"临时文件已删除: {temp_path}")
                except Exception as e:
                    self.ap.logger.warning(f"删除临时文件失败: {e}")

    async def create_image_message(self, image_data: bytes):
        """创建图片消息"""
        try:
            import base64

            # 转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            image_content = llm_entities.ContentElement.from_image_base64(image_base64)

            return llm_entities.Message(
                role='assistant',
                content=[image_content]
            )

        except Exception as e:
            self.ap.logger.error(f"创建图片消息失败: {e}")
            return None

    def get_user_id(self, query: Any) -> str:
        """获取用户ID"""
        try:
            return f"{query.launcher_type.value}_{query.sender_id}"
        except:
            return "unknown_user"
    
    def get_chat_id(self, query: Any) -> str:
        """获取聊天ID"""
        try:
            return f"{query.launcher_type.value}_{query.sender_id}"
        except:
            return "unknown_chat" 