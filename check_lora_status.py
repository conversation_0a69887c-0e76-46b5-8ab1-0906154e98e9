#!/usr/bin/env python3
"""
LoRA模型状态检查工具
用于检查本地LoRA模型的状态，搜索和下载Civitai模型
"""

import asyncio
import sys
import os
import argparse
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from pkg.workers.shared.shared_lora_manager import SharedLoraManager
from pkg.workers.shared.civitai_client import civitai_client


async def check_model_status(model_name: str):
    """检查单个模型状态"""
    manager = SharedLoraManager()
    status = manager.check_lora_status(model_name)
    
    if "error" in status:
        print(f"❌ {status['error']}")
        return
    
    print(f"📊 模型状态: {model_name}")
    print(f"🏷️ 分类: {status['category']}")
    print(f"📍 位置: {'本地' if status['is_local'] else '远程'}")
    print(f"🔘 状态: {'激活' if status['is_active'] else '未激活'}")
    print(f"⚖️ 权重: {status['weight']}")
    
    if status['is_local']:
        print(f"📁 文件路径: {status['file_path']}")
        print(f"📄 文件存在: {'是' if status.get('file_exists', False) else '否'}")
        if status.get('file_size'):
            size_mb = status['file_size'] / (1024 * 1024)
            print(f"📏 文件大小: {size_mb:.1f} MB")
    
    if status['civitai_id']:
        print(f"🌐 Civitai ID: {status['civitai_id']}")
        if status['rating']:
            print(f"⭐ 评分: {status['rating']:.1f}")
        if status['downloads']:
            print(f"⬇️ 下载量: {status['downloads']:,}")
    
    if status['trigger_words']:
        print(f"🏷️ 触发词: {', '.join(status['trigger_words'][:5])}")
    
    if status['description']:
        desc = status['description'][:200] + "..." if len(status['description']) > 200 else status['description']
        print(f"📝 描述: {desc}")


async def list_all_models():
    """列出所有模型"""
    manager = SharedLoraManager()
    models = manager.get_all_models()
    
    if not models:
        print("📭 没有找到任何LoRA模型")
        return
    
    print(f"📋 共找到 {len(models)} 个LoRA模型:")
    print()
    
    # 按分类分组
    categories = {}
    for model in models:
        category = model.category.value
        if category not in categories:
            categories[category] = []
        categories[category].append(model)
    
    for category, category_models in categories.items():
        print(f"🏷️ {category.upper()} ({len(category_models)} 个模型)")
        for model in category_models:
            status = "🟢" if model.is_active else "🔴"
            location = "📁" if model.is_local else "☁️"
            rating = f"⭐{model.rating:.1f}" if model.rating else ""
            print(f"  {status}{location} {model.name} (权重: {model.weight}) {rating}")
        print()


async def search_civitai(query: str, limit: int = 10):
    """搜索Civitai模型"""
    print(f"🔍 在Civitai搜索: {query}")
    
    models = await civitai_client.search_models(
        query=query,
        types="LORA",
        sort="Highest Rated",
        limit=limit,
        nsfw=False
    )
    
    if not models:
        print(f"❌ 未找到匹配 '{query}' 的模型")
        return
    
    print(f"✅ 找到 {len(models)} 个模型:")
    print()
    
    for i, model in enumerate(models, 1):
        print(f"{i}. {model.name}")
        print(f"   👤 作者: {model.creator}")
        print(f"   ⭐ 评分: {model.rating:.1f}")
        print(f"   ⬇️ 下载: {model.download_count:,}")
        print(f"   🏷️ 标签: {', '.join(model.tags[:5])}")
        if model.description:
            desc = model.description[:100] + "..." if len(model.description) > 100 else model.description
            print(f"   📝 描述: {desc}")
        print(f"   🌐 链接: https://civitai.com/models/{model.id}")
        print()


async def update_from_civitai(query: str, limit: int = 20):
    """从Civitai更新模型信息"""
    manager = SharedLoraManager()
    result = await manager.update_from_civitai(query=query, limit=limit)
    print(result)


async def download_model(model_name: str):
    """下载模型"""
    manager = SharedLoraManager()
    local_path = await manager.download_civitai_model(model_name)
    
    if local_path:
        print(f"✅ 模型下载成功: {model_name}")
        print(f"📁 保存路径: {local_path}")
    else:
        print(f"❌ 下载失败: {model_name}")
        print("可能原因: 模型不存在、已是本地模型或网络问题")


async def main():
    parser = argparse.ArgumentParser(description="LoRA模型状态检查工具")
    parser.add_argument("command", choices=["status", "list", "search", "update", "download"], 
                       help="操作命令")
    parser.add_argument("target", nargs="?", help="目标模型名称或搜索关键词")
    parser.add_argument("--limit", type=int, default=10, help="搜索结果限制数量")
    
    args = parser.parse_args()
    
    try:
        if args.command == "status":
            if not args.target:
                print("❌ 请指定要检查的模型名称")
                print("用法: python check_lora_status.py status <模型名>")
                return
            await check_model_status(args.target)
        
        elif args.command == "list":
            await list_all_models()
        
        elif args.command == "search":
            if not args.target:
                print("❌ 请指定搜索关键词")
                print("用法: python check_lora_status.py search <关键词>")
                return
            await search_civitai(args.target, args.limit)
        
        elif args.command == "update":
            query = args.target or "flux"
            await update_from_civitai(query, args.limit)
        
        elif args.command == "download":
            if not args.target:
                print("❌ 请指定要下载的模型名称")
                print("用法: python check_lora_status.py download <模型名>")
                return
            await download_model(args.target)
    
    except KeyboardInterrupt:
        print("\n⏹️ 操作已取消")
    except Exception as e:
        print(f"❌ 操作失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
