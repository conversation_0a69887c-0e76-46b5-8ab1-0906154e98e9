#!/usr/bin/env python3
"""
触发词配置检查工具
检查当前的触发词和@bot配置是否正确
"""

import json
import os
import sys
from pathlib import Path

def check_pipeline_config():
    """检查pipeline配置"""
    config_files = [
        "templates/default-pipeline-config.json",
        "templates/default-pipeline-config.json.backup",
        "data/config/pipeline.json"  # 如果存在的话
    ]
    
    print("🔍 检查Pipeline配置文件...")
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"\n📄 检查文件: {config_file}")
            
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                trigger_config = config.get('trigger', {})
                group_rules = trigger_config.get('group-respond-rules', {})
                
                print(f"  ✅ at: {group_rules.get('at', 'NOT_SET')}")
                print(f"  ✅ atbot-with-prefix: {group_rules.get('atbot-with-prefix', 'NOT_SET')}")
                print(f"  ✅ prefix: {group_rules.get('prefix', 'NOT_SET')}")
                print(f"  ✅ random: {group_rules.get('random', 'NOT_SET')}")
                
                # 安全性检查
                if group_rules.get('at', False) and not group_rules.get('atbot-with-prefix', False):
                    print("  ⚠️  警告: 'at' 为 true 但 'atbot-with-prefix' 为 false，可能导致单纯@bot就触发")
                
                if group_rules.get('random', 0) > 0:
                    print(f"  ⚠️  警告: 'random' 设置为 {group_rules.get('random')}，可能随机触发")
                
                if not group_rules.get('prefix', []):
                    print("  ⚠️  警告: 'prefix' 为空，没有设置触发词")
                
            except Exception as e:
                print(f"  ❌ 读取配置文件失败: {e}")
        else:
            print(f"📄 文件不存在: {config_file}")

def check_unified_routing_config():
    """检查统一路由配置"""
    config_file = "config/unified_routing.yaml"
    
    print(f"\n🔍 检查统一路由配置: {config_file}")
    
    if os.path.exists(config_file):
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            unified_routing = config.get('unified_routing', {})
            level_1 = unified_routing.get('level_1', {})
            
            print(f"  ✅ level_1 enabled: {level_1.get('enabled', 'NOT_SET')}")
            print(f"  ✅ keywords: {level_1.get('keywords', 'NOT_SET')}")
            
            keywords = level_1.get('keywords', {})
            if keywords:
                for keyword, workflow in keywords.items():
                    print(f"    - {keyword} -> {workflow}")
            
        except Exception as e:
            print(f"  ❌ 读取配置文件失败: {e}")
    else:
        print(f"  📄 文件不存在: {config_file}")

def check_trigger_logic():
    """检查触发逻辑代码"""
    print(f"\n🔍 检查触发逻辑代码...")
    
    # 检查atbot_with_prefix规则
    atbot_file = "pkg/pipeline/resprule/rules/atbot_with_prefix.py"
    if os.path.exists(atbot_file):
        print(f"  ✅ atbot_with_prefix 规则文件存在")
        
        with open(atbot_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键逻辑
        if "has_at_bot = message_chain.has" in content:
            print("  ✅ @bot检查逻辑存在")
        else:
            print("  ❌ @bot检查逻辑缺失")
            
        if "has_prefix = False" in content:
            print("  ✅ 触发词检查逻辑存在")
        else:
            print("  ❌ 触发词检查逻辑缺失")
            
        if "if not has_prefix:" in content:
            print("  ✅ 触发词验证逻辑存在")
        else:
            print("  ❌ 触发词验证逻辑缺失")
    else:
        print(f"  ❌ atbot_with_prefix 规则文件不存在")
    
    # 检查统一路由系统
    routing_file = "pkg/core/workflow/unified_routing_system.py"
    if os.path.exists(routing_file):
        print(f"  ✅ 统一路由系统文件存在")
        
        with open(routing_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "if not level_1_result:" in content:
            print("  ✅ 第一级路由验证逻辑存在")
        else:
            print("  ❌ 第一级路由验证逻辑缺失")
            
        if "return None" in content:
            print("  ✅ 无触发词返回None逻辑存在")
        else:
            print("  ❌ 无触发词返回None逻辑缺失")
    else:
        print(f"  ❌ 统一路由系统文件不存在")

def main():
    print("🔍 LangBot 触发词配置检查工具")
    print("=" * 50)
    
    # 检查当前工作目录
    if not os.path.exists("pkg") or not os.path.exists("templates"):
        print("❌ 请在LangBot项目根目录下运行此脚本")
        sys.exit(1)
    
    check_pipeline_config()
    check_unified_routing_config()
    check_trigger_logic()
    
    print("\n" + "=" * 50)
    print("🎯 检查完成")
    print("\n📋 安全建议:")
    print("1. 确保 'atbot-with-prefix' 为 true")
    print("2. 确保 'at' 为 false（避免单纯@bot触发）")
    print("3. 确保 'prefix' 包含正确的触发词")
    print("4. 确保 'random' 为 0（避免随机触发）")

if __name__ == "__main__":
    main()
