# PRD-UniversalAgain.md

## 一、需求背景

所有图像生成工作流（Kontext、Flux、Kontext API等）需要支持"再次生成/again"功能，允许用户无需重新输入提示词和图片，直接复用上一次的生成参数，实现快速迭代和体验优化。当前实现仅支持内存级别的参数复用，未实现跨进程/重启的持久化，导致"再次生成"在部分场景下不可用。

## 二、功能描述

- 用户在完成任意工作流（Kontext、Flux、Kontext API）生成后，可以通过"再次生成/again"等指令，快速复用上一次的提示词、图片和参数，重新生成图片。
- 支持多种触发指令（如"again"、"再来一次"、"repeat"等）。
- 支持"修改提示词"功能，允许用户在复用图片的基础上，仅修改提示词重新生成。
- 支持所有工作流类型：Kontext、Flux（TEXT_ONLY、CONTROL_ONLY、REFERENCE_ONLY、CONTROL_REFERENCE）、Kontext API。

## 三、需求范围定义（REQ-01）

- 记录每次生成的核心参数（提示词、图片、参数、时间戳、工作流类型等）。
- 每次生成后，将参数/会话数据序列化保存到`temp/`目录（如`temp/last_session_{user_id}.json`）。
- "again"指令优先从`temp`目录读取最近一次的会话/参数数据。
- 支持1小时内的历史记录查找，超时自动清理。
- 支持多用户并发，数据隔离。
- 支持跨工作流类型的参数复用（如从Kontext切换到Flux）。

## 四、接口需求（REQ-02）

- 提供保存会话/参数的接口（如`save_last_session(user_id, data)`）。
- 提供读取最近一次会话/参数的接口（如`load_last_session(user_id)`）。
- 支持"再次生成/again"指令的统一入口，自动判断是否有可用历史。
- 支持"修改提示词"指令的参数更新与复用。
- 支持工作流类型识别和参数适配。

## 五、边界条件（REQ-03）

- 若无历史记录或历史已过期，需友好提示用户"无可用历史"。
- 若历史图片文件丢失或损坏，需自动忽略并提示。
- 仅支持最近一次生成的参数复用，不支持多历史版本回溯。
- 不支持跨用户/跨会话复用。
- 支持跨工作流类型的参数复用，但需进行参数适配和验证。

## 六、异常处理（REQ-04）

- 读取历史失败时，需捕获异常并记录日志。
- 持久化写入失败时，需重试并告警。
- 用户请求"again"但无历史时，需返回明确提示。
- 工作流类型不匹配时，需进行参数转换或提示用户。

## 七、持久化与数据安全（DES-01~DES-02）

- 所有会话/参数数据需序列化为JSON格式，存储于`temp/`目录。
- 文件命名建议：`temp/last_session_{user_id}.json`，确保多用户隔离。
- 定期清理过期历史，避免磁盘膨胀。
- 支持工作流类型、参数版本等元数据记录。

## 八、任务编号引用

- 需求范围定义：REQ-01
- 接口定义：REQ-02
- 边界条件：REQ-03
- 场景描述：REQ-04
- 数据流/结构设计：DES-01、DES-02
- 状态管理实现：DEV-02
- 通信模块实现：DEV-03

---

## 九、完成状态标注（示例）

```
REQ-01 需求范围定义
✅ **完成状态**：[universal_again]REQ-01完成
📌 **验证点**：
- 是否支持跨进程/重启的历史复用
- 是否支持多用户隔离
- 是否支持跨工作流类型复用
- 是否有友好异常提示
```

---

## 十、附录：典型交互流程

1. 用户完成任意工作流生成（Kontext/Flux/Kontext API）
2. 系统自动保存参数到`temp/last_session_{user_id}.json`
3. 用户发送"again"指令
4. 系统优先读取持久化数据，复用参数生成图片
5. 若无历史，提示"无可用历史记录"
6. 支持跨工作流类型的参数复用和适配 