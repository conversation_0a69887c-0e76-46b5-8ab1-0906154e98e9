"""
Flux LoRA 集成器 V2 - 简洁高效版本

设计原则：
1. 简单明确的节点连接逻辑
2. 严格的数据类型验证
3. 清晰的错误处理
4. 支持多种 Flux 工作流类型
"""

import logging
import copy
from typing import Dict, List, Optional, Any, Tuple

from .flux_workflow_models import LoRAConfig, FluxParameters, LoRACategory
from ...workers.shared.shared_lora_manager import SharedLoraManager


class LoRAIntegrationV2:
    """Flux LoRA 集成器 V2 - 重新设计的简洁版本"""
    
    def __init__(self):
        # 使用主应用的logger，确保日志能正确输出
        self.logger = logging.getLogger('langbot')
        self.lora_manager = SharedLoraManager()
        
    async def apply_loras_to_workflow(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig]) -> Tuple[Dict[str, Any], str]:
        """
        将 LoRA 模型应用到工作流中

        Args:
            workflow_data: 原始工作流数据
            loras: LoRA 配置列表

        Returns:
            Tuple[Dict[str, Any], str]: (更新后的工作流数据, 状态消息)
        """
        # 如果没有LoRA模型，直接返回原始工作流
        if not loras:
            self.logger.info("没有LoRA模型需要应用")
            return workflow_data, "✅ 工作流准备完成（无LoRA）"
            
        try:
            # 深拷贝工作流数据，避免污染原始数据
            updated_workflow = copy.deepcopy(workflow_data)
            
            # 验证工作流数据完整性
            self._validate_workflow_structure(updated_workflow)
            
            # 查找Power Lora Loader节点（现在是默认配置）
            power_lora_node_id = self._find_power_lora_loader_node(updated_workflow)

            if power_lora_node_id:
                # 使用Power Lora Loader方案（推荐方案）
                success_count = await self._apply_loras_to_power_loader(updated_workflow, loras, power_lora_node_id)
                approach = "Power Lora Loader"
                self.logger.info(f"✅ 使用Power Lora Loader方案，简洁高效")
            else:
                # 如果没有Power Lora Loader，提示用户更新工作流
                self.logger.warning("⚠️ 未找到Power Lora Loader节点，建议更新工作流模板")
                # 分析工作流类型和结构
                workflow_info = self._analyze_workflow_structure(updated_workflow)
                # 使用传统的链式LoRA方案（兼容性方案）
                success_count = await self._apply_loras_to_structure(updated_workflow, loras, workflow_info)
                approach = "链式LoRA（兼容模式）"
            
            if success_count == 0:
                return workflow_data, "⚠️ 没有成功应用任何LoRA模型，使用原始工作流"
            
            # 生成成功消息
            lora_names = [lora.name for lora in loras[:success_count]]
            success_message = f"✅ 使用{approach}成功应用 {success_count} 个LoRA模型: {', '.join(lora_names[:2])}{'...' if len(lora_names) > 2 else ''}"
            
            return updated_workflow, success_message
            
        except Exception as e:
            self.logger.error(f"LoRA应用失败: {e}")
            error_message = f"⚠️ LoRA模型应用失败，已降级为标准工作流。错误: {str(e)}"
            return workflow_data, error_message
    
    def _find_power_lora_loader_node(self, workflow_data: Dict[str, Any]) -> Optional[str]:
        """查找Power Lora Loader节点"""
        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and node_data.get("class_type") == "Power Lora Loader (rgthree)":
                self.logger.info(f"找到Power Lora Loader节点: {node_id}")
                return node_id
        return None
    
    async def _apply_loras_to_power_loader(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig], power_lora_node_id: str) -> int:
        """使用Power Lora Loader方案应用LoRA - 动态配置版本"""
        try:
            # 获取ComfyUI可用的LoRA列表
            available_loras = await self._get_comfyui_lora_list()

            power_lora_node = workflow_data[power_lora_node_id]
            inputs = power_lora_node["inputs"]

            self.logger.info(f"🎨 开始配置Power Lora Loader节点 {power_lora_node_id}")

            # 清除现有的动态LoRA配置（保留固定字段）
            fixed_keys = {"PowerLoraLoaderHeaderWidget", "➕ Add Lora", "model", "clip"}
            keys_to_remove = [key for key in inputs.keys() if key not in fixed_keys]
            for key in keys_to_remove:
                del inputs[key]

            success_count = 0

            # 限制LoRA数量为最佳实践的3个（性能和稳定性考虑）
            max_loras = min(len(loras), 3)
            self.logger.info(f"🎯 应用 {max_loras} 个LoRA模型（最佳实践：≤3个，优先风格类）")

            # 配置选中的LoRA模型
            for i, lora in enumerate(loras[:max_loras]):
                # 使用智能匹配找到ComfyUI中的实际文件名
                matched_filename = self._find_matching_comfyui_filename(lora, available_loras or [])

                if not matched_filename:
                    self.logger.warning(f"⚠️ 跳过无法匹配的LoRA: {lora.name} ({lora.filename})")
                    if available_loras:
                        continue
                    matched_filename = lora.filename

                # 动态添加LoRA配置（rgthree格式）
                lora_key = f"lora_{i + 1}"
                strength_key = f"strength_{i + 1}"

                inputs[lora_key] = matched_filename
                inputs[strength_key] = round(lora.weight, 3)

                success_count += 1
                # self.logger.info(f"✅ LoRA {i + 1}: {lora.name} ({matched_filename}) - 强度: {lora.weight}")  # 隐藏单个LoRA配置信息

            # 显示最终配置
            if success_count > 0:
                active_loras = []
                for i in range(success_count):
                    lora_name = inputs.get(f"lora_{i + 1}")
                    strength = inputs.get(f"strength_{i + 1}")
                    if lora_name:
                        active_loras.append(f"{lora_name}({strength})")

                self.logger.info(f"🎨 Power Lora Loader配置完成: {', '.join(active_loras)}")
            else:
                self.logger.info(f"🎨 Power Lora Loader: 无LoRA配置")

            return success_count

        except Exception as e:
            self.logger.error(f"❌ Power Lora Loader配置失败: {e}")
            return 0

    def _validate_workflow_structure(self, workflow_data: Dict[str, Any]):
        """验证工作流数据结构的完整性"""
        if not isinstance(workflow_data, dict):
            raise ValueError(f"工作流数据必须是字典类型，当前类型: {type(workflow_data)}")
        
        # 检查每个节点的数据类型
        for node_id, node_data in workflow_data.items():
            if not isinstance(node_data, dict):
                raise ValueError(f"节点 {node_id} 的数据类型错误: {type(node_data)}, 值: {node_data}")
            
            if "class_type" not in node_data:
                raise ValueError(f"节点 {node_id} 缺少 class_type 字段")
            
            if not isinstance(node_data.get("inputs", {}), dict):
                raise ValueError(f"节点 {node_id} 的 inputs 字段必须是字典类型")
    
    def _analyze_workflow_structure(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析工作流结构，识别关键节点"""
        workflow_info = {
            "type": "unknown",
            "unet_loader": None,
            "clip_loader": None,
            "sampler": None,
            "existing_loras": [],
            "model_chain": [],
            "clip_chain": []
        }
        
        # 查找关键节点
        for node_id, node_data in workflow_data.items():
            class_type = node_data.get("class_type", "")
            
            # UNet 加载器
            if class_type == "UNETLoader":
                workflow_info["unet_loader"] = node_id
                
            # CLIP 加载器
            elif class_type == "DualCLIPLoader":
                workflow_info["clip_loader"] = node_id
                
            # 采样器
            elif "Sampler" in class_type or "KSampler" in class_type:
                workflow_info["sampler"] = node_id
                
            # 现有的 LoRA 节点
            elif "Lora" in class_type:
                workflow_info["existing_loras"].append(node_id)
        
        # 确定工作流类型
        if "ControlNet" in str(workflow_data):
            workflow_info["type"] = "controlnet"
        elif "CLIPVision" in str(workflow_data):
            workflow_info["type"] = "redux"
        else:
            workflow_info["type"] = "default"
        
        self.logger.info(f"工作流分析结果: {workflow_info}")
        return workflow_info
    
    async def _apply_loras_to_structure(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig], workflow_info: Dict[str, Any]) -> int:
        """根据工作流结构应用 LoRA 模型"""
        if not workflow_info["unet_loader"]:
            raise ValueError("未找到 UNETLoader 节点，无法应用 LoRA")

        # 清理现有的 LoRA 节点
        self._remove_existing_loras(workflow_data, workflow_info["existing_loras"])

        # 构建 LoRA 链
        success_count = await self._build_lora_chain(workflow_data, loras, workflow_info)

        return success_count
    
    def _remove_existing_loras(self, workflow_data: Dict[str, Any], existing_loras: List[str]):
        """移除现有的 LoRA 节点"""
        for lora_id in existing_loras:
            if lora_id in workflow_data:
                del workflow_data[lora_id]
                self.logger.info(f"移除现有LoRA节点: {lora_id}")
    
    async def _build_lora_chain(self, workflow_data: Dict[str, Any], loras: List[LoRAConfig], workflow_info: Dict[str, Any]) -> int:
        """构建 LoRA 节点链"""
        unet_loader_id = workflow_info["unet_loader"]
        clip_loader_id = workflow_info["clip_loader"]

        # 获取ComfyUI可用的LoRA列表
        available_loras = await self._get_comfyui_lora_list()

        # 当前模型输出（Flux工作流只需要模型链，不需要CLIP链）
        current_model_output = [unet_loader_id, 0]
        current_clip_output = None  # Flux工作流不使用LoRA的CLIP输出

        success_count = 0

        for i, lora in enumerate(loras):
            lora_node_id = f"lora_{i + 1}"

            # 创建 LoRA 节点
            lora_node = await self._create_lora_node(lora, current_model_output, current_clip_output, available_loras)

            if lora_node:
                workflow_data[lora_node_id] = lora_node

                # 更新模型输出连接（LoraLoaderModelOnly只有模型输出）
                current_model_output = [lora_node_id, 0]

                success_count += 1
                self.logger.info(f"成功添加LoRA节点: {lora_node_id} ({lora.name})")
            else:
                self.logger.warning(f"跳过无法匹配的LoRA: {lora.name} ({lora.filename})")

        # 更新依赖节点的连接
        if success_count > 0:
            self._update_dependent_connections(workflow_data, workflow_info, current_model_output, None)

            # 验证连接完整性
            self._validate_workflow_connections(workflow_data)

        return success_count
    
    async def _create_lora_node(self, lora: LoRAConfig, model_input: List, clip_input: Optional[List], available_loras: List[str] = None) -> Optional[Dict[str, Any]]:
        """创建单个 LoRA 节点"""
        try:
            # 使用智能匹配找到ComfyUI中的实际文件名
            matched_filename = self._find_matching_comfyui_filename(lora, available_loras or [])

            if not matched_filename:
                self.logger.warning(f"🔧 [DEBUG] 无法为LoRA找到匹配的文件名: {lora.name} ({lora.filename})")
                # 如果有可用列表但没找到匹配，跳过这个LoRA
                if available_loras:
                    return None
                # 如果没有可用列表，使用原始文件名
                matched_filename = lora.filename

            self.logger.info(f"🔧 [DEBUG] 创建LoRA节点: {lora.name}, 原始文件名: {lora.filename}, 匹配文件名: {matched_filename}")

            # 对于Flux工作流，默认使用LoraLoaderModelOnly
            # 这与原始工作流模板保持一致
            return {
                "class_type": "LoraLoaderModelOnly",
                "inputs": {
                    "model": model_input,
                    "lora_name": matched_filename,
                    "strength_model": round(lora.weight, 3)
                },
                "_meta": {
                    "title": f"LoRA: {lora.name}"
                }
            }
        except Exception as e:
            self.logger.error(f"创建LoRA节点失败: {e}")
            return None

    async def _get_comfyui_lora_list(self) -> List[str]:
        """获取ComfyUI中实际可用的LoRA列表"""
        try:
            import aiohttp
            # 使用ComfyUI的API获取可用的LoRA列表
            api_url = "http://127.0.0.1:8188/object_info/LoraLoader"

            async with aiohttp.ClientSession() as session:
                async with session.get(api_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        # 提取LoRA文件名列表
                        lora_list = data.get("LoraLoader", {}).get("input", {}).get("required", {}).get("lora_name", [[], {}])[0]
                        return lora_list
                    else:
                        self.logger.warning(f"获取ComfyUI LoRA列表失败: HTTP {response.status}")
                        return []
        except Exception as e:
            self.logger.error(f"查询ComfyUI LoRA列表失败: {e}")
            return []

    def _find_matching_comfyui_filename(self, lora_config: LoRAConfig, available_loras: List[str]) -> Optional[str]:
        """
        为LoRA配置找到在ComfyUI中匹配的实际文件名

        Args:
            lora_config: LoRA配置对象
            available_loras: ComfyUI中可用的LoRA文件名列表

        Returns:
            Optional[str]: 匹配的文件名，如果没找到返回None
        """
        if not available_loras:
            # 如果无法获取ComfyUI列表，使用配置文件中的文件名
            self.logger.warning(f"🔧 [DEBUG] 无法获取ComfyUI LoRA列表，使用配置文件名: {lora_config.filename}")
            return lora_config.filename

        # 获取原始文件名
        import os
        original_filename = os.path.basename(lora_config.filename)

        # 1. 首先尝试精确匹配配置文件中的文件名
        if original_filename in available_loras:
            return original_filename

        # 2. 生成候选文件名并尝试匹配
        candidates = self._generate_filename_candidates(original_filename, lora_config)

        # 3. 精确匹配候选文件名
        for candidate in candidates:
            if candidate in available_loras:
                self._suggest_config_fix(lora_config, candidate)
                return candidate

        # 4. 部分匹配（基于文件名相似度）
        for candidate in candidates:
            for available in available_loras:
                if self._is_filename_similar(candidate, available):
                    self._suggest_config_fix(lora_config, available)
                    return available

        # 5. 基于LoRA名称的智能匹配
        lora_name_lower = lora_config.name.lower()
        for available in available_loras:
            available_lower = available.lower()
            if lora_name_lower in available_lower or available_lower in lora_name_lower:
                self._suggest_config_fix(lora_config, available)
                return available

        # 6. 基于触发词的匹配
        for trigger_word in lora_config.trigger_words:
            trigger_lower = trigger_word.lower()
            for available in available_loras:
                available_lower = available.lower()
                if trigger_lower in available_lower:
                    self._suggest_config_fix(lora_config, available)
                    return available

        # 7. 如果都没找到，记录详细信息并建议修正
        self.logger.warning(f"未找到匹配的ComfyUI文件: {lora_config.name} ({original_filename})")
        self.logger.warning(f"🔧 [DEBUG] 建议检查配置文件或运行: python3 discover_lora_models.py")
        self.logger.warning(f"🔧 [DEBUG] 可用文件示例: {available_loras[:5]}...")
        return None

    def _suggest_config_fix(self, lora_config: LoRAConfig, correct_filename: str):
        """建议配置文件修正"""
        if lora_config.filename != correct_filename:
            self.logger.info(f"💡 [建议] 配置文件修正: {lora_config.name}")
            self.logger.info(f"   当前文件名: {lora_config.filename}")
            self.logger.info(f"   建议文件名: {correct_filename}")
            self.logger.info(f"   修正命令: python3 analyze_lora_models.py update \"{lora_config.name}\" --filename \"{correct_filename}\"")

    def _generate_filename_candidates(self, filename: str, lora_config: LoRAConfig) -> List[str]:
        """生成文件名候选列表"""
        candidates = []

        # 基于原始文件名的变体
        candidates.extend([
            filename,
            filename.replace('.safetensors', ''),
            filename + '.safetensors' if not filename.endswith('.safetensors') else filename,
        ])

        # 基于LoRA名称的变体
        lora_name = lora_config.name
        candidates.extend([
            lora_name,
            lora_name + '.safetensors',
            lora_name.replace(' ', '_'),
            lora_name.replace(' ', '_') + '.safetensors',
            lora_name.replace(' ', '-'),
            lora_name.replace(' ', '-') + '.safetensors',
        ])

        # 移除重复项
        return list(set(candidates))

    def _is_filename_similar(self, name1: str, name2: str) -> bool:
        """检查两个文件名是否相似"""
        # 移除扩展名进行比较
        base1 = name1.replace('.safetensors', '').replace('.ckpt', '').replace('.pt', '')
        base2 = name2.replace('.safetensors', '').replace('.ckpt', '').replace('.pt', '')

        # 标准化（移除特殊字符，转小写）
        import re
        base1_clean = re.sub(r'[_\-\s]+', '', base1.lower())
        base2_clean = re.sub(r'[_\-\s]+', '', base2.lower())

        # 检查相似度
        return (base1_clean in base2_clean or base2_clean in base1_clean or
                base1_clean == base2_clean)
    
    def _update_dependent_connections(self, workflow_data: Dict[str, Any], workflow_info: Dict[str, Any],
                                    final_model_output: List, final_clip_output: Optional[List]):
        """更新依赖节点的连接"""
        unet_loader_id = workflow_info["unet_loader"]
        clip_loader_id = workflow_info["clip_loader"]
        existing_loras = workflow_info["existing_loras"]

        for node_id, node_data in workflow_data.items():
            if not isinstance(node_data, dict) or "inputs" not in node_data:
                continue

            inputs = node_data["inputs"]
            updated = False

            # 检查所有输入字段
            for input_name, input_value in inputs.items():
                if isinstance(input_value, list) and len(input_value) >= 2:
                    source_node_id = input_value[0]

                    # 更新直接连接到UNETLoader的模型连接
                    if source_node_id == unet_loader_id and input_name == "model":
                        inputs[input_name] = final_model_output
                        self.logger.info(f"更新节点 {node_id} 的模型连接: {input_name}")
                        updated = True

                    # 更新直接连接到CLIPLoader的CLIP连接
                    elif final_clip_output and source_node_id == clip_loader_id and input_name == "clip":
                        inputs[input_name] = final_clip_output
                        self.logger.info(f"更新节点 {node_id} 的CLIP连接: {input_name}")
                        updated = True

                    # 更新连接到被删除LoRA节点的连接
                    elif source_node_id in existing_loras:
                        # 根据输出索引确定连接类型
                        output_index = input_value[1]
                        if output_index == 0:  # 模型输出
                            inputs[input_name] = final_model_output
                            self.logger.info(f"更新节点 {node_id} 的连接 {input_name}: 从删除的LoRA节点 {source_node_id} 重定向到最终模型输出")
                            updated = True
                        elif output_index == 1 and final_clip_output:  # CLIP输出
                            inputs[input_name] = final_clip_output
                            self.logger.info(f"更新节点 {node_id} 的连接 {input_name}: 从删除的LoRA节点 {source_node_id} 重定向到最终CLIP输出")
                            updated = True

            if updated:
                self.logger.info(f"✅ 节点 {node_id} 的连接已更新")

    def _validate_workflow_connections(self, workflow_data: Dict[str, Any]):
        """验证工作流连接的完整性"""
        self.logger.info("🔍 验证工作流连接完整性...")

        all_node_ids = set(workflow_data.keys())
        broken_connections = []

        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and "inputs" in node_data:
                inputs = node_data["inputs"]
                for input_name, input_value in inputs.items():
                    if isinstance(input_value, list) and len(input_value) >= 2:
                        source_node_id = input_value[0]
                        if source_node_id not in all_node_ids:
                            broken_connections.append({
                                "target_node": node_id,
                                "input_name": input_name,
                                "missing_source": source_node_id,
                                "class_type": node_data.get("class_type", "unknown")
                            })

        if broken_connections:
            self.logger.error(f"❌ 发现 {len(broken_connections)} 个断开的连接:")
            for conn in broken_connections:
                self.logger.error(f"   节点 {conn['target_node']} ({conn['class_type']}) 的输入 {conn['input_name']} 引用了不存在的节点 {conn['missing_source']}")

            # 尝试修复断开的连接
            self._fix_broken_connections(workflow_data, broken_connections)
        else:
            self.logger.info("✅ 所有连接都完整")

    def _fix_broken_connections(self, workflow_data: Dict[str, Any], broken_connections: list):
        """尝试修复断开的连接"""
        self.logger.info("🔧 尝试修复断开的连接...")

        for conn in broken_connections:
            target_node_id = conn["target_node"]
            input_name = conn["input_name"]
            missing_source = conn["missing_source"]

            if target_node_id in workflow_data:
                # 对于模型输入，尝试连接到UNETLoader
                if input_name == "model":
                    # 查找UNETLoader节点
                    unet_loader = None
                    for node_id, node_data in workflow_data.items():
                        if node_data.get("class_type") == "UNETLoader":
                            unet_loader = node_id
                            break

                    if unet_loader:
                        workflow_data[target_node_id]["inputs"][input_name] = [unet_loader, 0]
                        self.logger.info(f"🔧 修复连接: 节点 {target_node_id} 的 {input_name} 连接到 UNETLoader {unet_loader}")

                # 对于其他类型的连接，记录但不自动修复
                else:
                    self.logger.warning(f"⚠️ 无法自动修复连接: 节点 {target_node_id} 的 {input_name} (缺失源: {missing_source})")

    def _should_use_clip_chain(self, workflow_data: Dict[str, Any], workflow_info: Dict[str, Any]) -> bool:
        """检查是否需要使用CLIP链"""
        # 检查是否有节点连接到现有LoRA的CLIP输出
        existing_loras = workflow_info.get("existing_loras", [])

        for node_id, node_data in workflow_data.items():
            if isinstance(node_data, dict) and "inputs" in node_data:
                inputs = node_data["inputs"]
                for input_name, input_value in inputs.items():
                    if isinstance(input_value, list) and len(input_value) >= 2:
                        source_node_id = input_value[0]
                        output_index = input_value[1]

                        # 如果有节点连接到LoRA的CLIP输出（索引1），则需要CLIP链
                        if source_node_id in existing_loras and output_index == 1:
                            self.logger.info(f"检测到CLIP链需求: 节点 {node_id} 连接到 LoRA {source_node_id} 的CLIP输出")
                            return True

        # 默认情况下，Flux工作流通常不需要CLIP链
        self.logger.info("未检测到CLIP链需求，使用仅模型LoRA节点")
        return False

    async def select_loras_for_prompt(self, params: FluxParameters, max_loras: int = 3,
                                    use_civitai: bool = False, civitai_query: str = "") -> List[LoRAConfig]:
        """
        根据提示词和参数选择合适的LoRA模型

        Args:
            params: Flux参数
            max_loras: 最大LoRA数量
            use_civitai: 是否使用Civitai搜索
            civitai_query: Civitai搜索关键词

        Returns:
            List[LoRAConfig]: 选择的LoRA配置列表
        """
        try:
            selected_loras = []

            # 第一步：本地模型匹配
            search_query = civitai_query if civitai_query else params.prompt
            # self.logger.info(f"🔧 [DEBUG] 搜索查询: '{search_query}'")  # 隐藏搜索查询调试信息

            # 检查LoRA管理器状态（调试信息已隐藏）
            # total_models = len(self.lora_manager.lora_models)
            # active_models = len([m for m in self.lora_manager.lora_models.values() if m.is_active])
            # self.logger.info(f"🔧 [DEBUG] LoRA管理器状态: 总模型数={total_models}, 活跃模型数={active_models}")  # 隐藏LoRA状态调试信息

            # 指定只匹配flux类型的模型
            selected_models = self.lora_manager.get_models_by_trigger(search_query, model_type="flux")
            self.logger.info(f"匹配到 {len(selected_models)} 个候选LoRA模型")

            # 智能选择和排序LoRA模型
            prioritized_models = self._prioritize_loras_by_style(selected_models, search_query)

            # 限制为最多3个LoRA（最佳实践）
            max_count = min(len(prioritized_models), 3)
            self.logger.info(f"根据风格优先级选择 {max_count} 个LoRA模型")

            # 如果启用Civitai且本地模型不足，尝试搜索下载
            if use_civitai and len(prioritized_models) < max_count:
                self.logger.info(f"本地模型不足，从Civitai搜索: {search_query}")
                await self._search_and_download_civitai_models(search_query, max_count - len(prioritized_models))
                # 重新获取模型（可能包含新下载的）
                selected_models = self.lora_manager.get_models_by_trigger(search_query)
                prioritized_models = self._prioritize_loras_by_style(selected_models, search_query)

            # 转换为LoRAConfig格式，限制数量
            for model in prioritized_models[:max_count]:
                lora_config = LoRAConfig(
                    name=model.name,
                    filename=model.filename,
                    weight=model.weight,
                    category=self._map_category(model.category.value),
                    trigger_words=model.trigger_words,
                    description=model.description,
                    file_path=model.file_path,
                    is_local=model.is_local,
                    is_active=model.is_active
                )

                # 设置Civitai信息
                if model.civitai_id:
                    lora_config.civitai_id = model.civitai_id
                if model.civitai_url:
                    lora_config.civitai_url = model.civitai_url
                if model.rating is not None:
                    lora_config.rating = float(model.rating)
                if model.downloads is not None:
                    lora_config.downloads = int(model.downloads)

                selected_loras.append(lora_config)

            # 如果没有找到合适的LoRA，添加默认的细节增强LoRA
            if not selected_loras:
                default_lora = self._get_default_detail_lora()
                if default_lora:
                    selected_loras.append(default_lora)

            self.logger.info(f"为提示词选择了 {len(selected_loras)} 个LoRA模型")
            for lora in selected_loras:
                self.logger.info(f"  - {lora.name} (权重: {lora.weight}, 分类: {lora.category.value})")

            return selected_loras

        except Exception as e:
            self.logger.error(f"选择LoRA模型失败: {e}")
            # 返回默认LoRA
            default_lora = self._get_default_detail_lora()
            return [default_lora] if default_lora else []

    def optimize_lora_weights(self, loras: List[LoRAConfig], quality_level: str) -> List[LoRAConfig]:
        """
        根据质量级别优化LoRA权重

        Args:
            loras: LoRA配置列表
            quality_level: 质量级别

        Returns:
            List[LoRAConfig]: 优化后的LoRA配置列表
        """
        if not loras:
            return []

        # 权重调整系数
        weight_multipliers = {
            "draft": 0.6,      # 草稿模式：降低权重
            "standard": 0.8,   # 标准模式：适中权重
            "high": 1.0,       # 高质量：正常权重
            "ultra": 1.2       # 超高质量：增强权重
        }

        multiplier = weight_multipliers.get(quality_level, 1.0)
        optimized_loras = []

        for lora in loras:
            optimized_lora = LoRAConfig(
                name=lora.name,
                filename=lora.filename,
                weight=min(1.0, lora.weight * multiplier),  # 确保不超过1.0
                category=lora.category,
                trigger_words=lora.trigger_words,
                description=lora.description,
                file_path=lora.file_path,
                civitai_id=lora.civitai_id,
                civitai_url=lora.civitai_url,
                rating=lora.rating,
                downloads=lora.downloads,
                is_local=lora.is_local,
                is_active=lora.is_active
            )
            optimized_loras.append(optimized_lora)

        self.logger.info(f"为质量级别 '{quality_level}' 优化LoRA权重 (系数: {multiplier})")
        return optimized_loras

    def _prioritize_loras_by_style(self, models: List, search_query: str) -> List:
        """
        根据提示词风格智能排序LoRA模型

        Args:
            models: 匹配的模型列表
            search_query: 搜索查询

        Returns:
            List: 按优先级排序的模型列表
        """
        if not models:
            return []

        # 分析提示词中的主要风格关键词
        query_lower = search_query.lower()
        style_keywords = {
            # 动漫/卡通风格
            'anime': ['anime', 'manga', '动漫', '漫画', '二次元', 'semi-realistic anime', 'niji',
                     'cartoon', '卡通', 'cel shading', 'flat color'],

            # 摄影/写实风格
            'photography': ['photography', 'photo', 'photographic', '摄影', '照片', 'realistic',
                           'photorealistic', '写实', '真实', 'hyperrealistic', 'lifelike'],

            # 绘画风格 - 数字艺术
            'digital_painting': ['digital painting', 'digital art', '数字绘画', '数字艺术', 'cg',
                                'concept art', '概念艺术', 'matte painting'],

            # 绘画风格 - 传统媒介
            'oil_painting': ['oil painting', '油画', 'oil on canvas', 'classical painting'],
            'watercolor': ['watercolor', 'watercolour', '水彩', 'aquarelle', 'wet on wet'],
            'acrylic': ['acrylic', '丙烯', 'acrylic painting', 'acrylic on canvas'],
            'chinese_painting': ['chinese painting', '中国画', '国画', 'ink wash', '水墨', 'sumi-e'],
            'marker': ['marker', '马克笔', 'copic', 'alcohol marker', 'felt tip'],

            # 艺术风格
            'illustration': ['illustration', '插画', 'illustrative', 'book illustration'],
            'abstract': ['abstract', '抽象', 'non-representational', 'geometric abstract'],
            'surreal': ['surreal', 'surrealism', '超现实', 'dreamlike', 'fantastical'],
            'collage': ['collage', '拼贴', '拼贴画', 'mixed media', 'photomontage'],

            # 特殊风格/主题
            'cyberpunk': ['cyberpunk', '赛博朋克', 'futuristic', 'sci-fi', 'neon', 'dystopian'],
            'steampunk': ['steampunk', '蒸汽朋克', 'victorian', 'brass', 'mechanical'],
            'fantasy': ['fantasy', '奇幻', 'magical', 'mythical', 'fairy tale'],

            # 技术特征
            'detail': ['detail', 'enhance', 'quality', 'upscale', '细节', '增强', '高清',
                      'high resolution', 'sharp', 'crisp'],

            # 内容类型
            'architecture': ['building', 'architecture', '建筑', 'skyscraper', 'cityscape',
                           'interior', 'exterior', 'structural'],
            'character': ['woman', 'man', 'person', 'character', '人物', '角色', 'portrait',
                         'figure', 'human'],
            'landscape': ['landscape', '风景', 'scenery', 'nature', 'outdoor', 'environment']
        }

        # 检测主要风格
        detected_styles = []
        for style, keywords in style_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                detected_styles.append(style)

        # 为每个模型计算优先级分数
        scored_models = []
        for model in models:
            score = 0.0

            # 1. 基础分类优先级（风格类 > 细节类 > 内容类）
            from pkg.workers.shared.shared_lora_manager import LoraCategory
            category_score = LoraCategory.get_priority_score(model.category)
            score += category_score

            # 2. 风格匹配加分
            model_category = model.category.value.lower()

            # 动漫/卡通风格匹配
            if 'anime' in detected_styles and model_category == 'anime':
                score += 200  # 高优先级：anime风格匹配

            # 摄影/写实风格匹配
            elif 'photography' in detected_styles and model_category in ['style', 'portrait']:
                score += 180  # 高优先级：摄影风格匹配

            # 绘画风格匹配
            elif any(style in detected_styles for style in ['digital_painting', 'oil_painting', 'watercolor', 'acrylic', 'chinese_painting']) and model_category in ['style', 'anime']:
                score += 170  # 高优先级：绘画风格匹配

            # 插画风格匹配
            elif 'illustration' in detected_styles and model_category in ['anime', 'style']:
                score += 160  # 中高优先级：插画风格匹配

            # 特殊主题风格匹配（cyberpunk, steampunk, fantasy等）
            elif any(style in detected_styles for style in ['cyberpunk', 'steampunk', 'fantasy']) and model_category in ['style', 'anime']:
                score += 150  # 中高优先级：特殊主题匹配

            # 抽象/超现实风格匹配
            elif any(style in detected_styles for style in ['abstract', 'surreal', 'collage']) and model_category == 'style':
                score += 140  # 中优先级：抽象风格匹配

            # 细节增强匹配
            elif 'detail' in detected_styles and model_category == 'detail':
                score += 120  # 中优先级：细节增强匹配

            # 3. 触发词精确匹配加分
            for trigger_word in model.trigger_words:
                if trigger_word.lower() in query_lower:
                    score += 50

            # 4. 模型评分加分
            if hasattr(model, 'rating') and model.rating:
                score += model.rating * 10

            # 5. 优先级模型加分
            if hasattr(model, 'is_priority') and model.is_priority:
                score += 100

            scored_models.append((model, score))

        # 按分数排序（降序）
        scored_models.sort(key=lambda x: x[1], reverse=True)

        # 确保分类平衡：优先选择不同分类的LoRA
        selected_models = []
        used_categories = set()

        # 第一轮：选择不同分类的高分模型
        for model, score in scored_models:
            if len(selected_models) >= 3:
                break
            category = model.category.value
            if category not in used_categories:
                selected_models.append(model)
                used_categories.add(category)

        # 第二轮：如果还有空位，选择剩余的高分模型
        for model, score in scored_models:
            if len(selected_models) >= 3:
                break
            if model not in selected_models:
                selected_models.append(model)

        return selected_models

    def _determine_optimal_lora_count(self, selected_models: List, search_query: str) -> int:
        """
        根据匹配质量和用户需求动态决定LoRA数量

        Args:
            selected_models: 匹配的模型列表
            search_query: 搜索查询

        Returns:
            int: 最优的LoRA数量
        """
        if not selected_models:
            return 1  # 至少返回一个默认LoRA

        # 基础规则
        base_count = min(len(selected_models), 3)

        # 根据查询复杂度调整
        query_words = search_query.split()
        if len(query_words) > 10:  # 复杂查询
            base_count = min(base_count + 1, 4)
        elif len(query_words) < 5:  # 简单查询
            base_count = max(base_count - 1, 1)

        # 根据模型质量调整
        high_quality_count = sum(1 for model in selected_models if (getattr(model, 'rating', None) or 0) > 4.0)
        if high_quality_count >= 2:
            base_count = min(base_count + 1, 4)

        return base_count

    def _get_default_detail_lora(self) -> Optional[LoRAConfig]:
        """
        获取默认的细节增强LoRA

        Returns:
            Optional[LoRAConfig]: 默认LoRA配置，如果没有找到则返回None
        """
        try:
            # 尝试获取一个通用的细节增强LoRA
            all_models = list(self.lora_manager.lora_models.values())
            self.logger.info(f"🔧 [DEBUG] 默认LoRA搜索: 总模型数={len(all_models)}")

            # 优先选择本地模型
            local_models = [m for m in all_models if m.is_local and m.is_active]
            self.logger.info(f"🔧 [DEBUG] 本地活跃模型数: {len(local_models)}")

            if local_models:
                # 选择第一个本地模型作为默认
                model = local_models[0]
                self.logger.info(f"🔧 [DEBUG] 选择默认LoRA: {model.name}")
                return LoRAConfig(
                    name=model.name,
                    filename=model.filename,
                    weight=0.7,  # 默认权重
                    category=self._map_category(model.category.value),
                    trigger_words=model.trigger_words,
                    description=model.description,
                    file_path=model.file_path,
                    is_local=model.is_local,
                    is_active=model.is_active
                )

            self.logger.warning("没有找到可用的默认LoRA模型")
            return None

        except Exception as e:
            self.logger.error(f"获取默认LoRA失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None

    def _map_category(self, category_str: str) -> LoRACategory:
        """
        将字符串分类映射到LoRACategory枚举

        Args:
            category_str: 分类字符串

        Returns:
            LoRACategory: 映射后的分类枚举
        """
        category_mapping = {
            "character": LoRACategory.CHARACTER,
            "style": LoRACategory.STYLE,
            "concept": LoRACategory.CONCEPT,
            "clothing": LoRACategory.CLOTHING,
            "pose": LoRACategory.POSE,
            "background": LoRACategory.BACKGROUND,
            "effect": LoRACategory.EFFECT,
            "tool": LoRACategory.TOOL,
            "other": LoRACategory.OTHER
        }

        return category_mapping.get(category_str.lower(), LoRACategory.OTHER)

    async def _search_and_download_civitai_models(self, query: str, max_models: int = 2):
        """
        从Civitai搜索并下载模型

        Args:
            query: 搜索关键词
            max_models: 最大下载模型数量
        """
        try:
            self.logger.info(f"从Civitai搜索模型: {query} (最多 {max_models} 个)")

            # 更新模型信息（搜索）
            result = await self.lora_manager.update_from_civitai(query=query, limit=max_models * 2)
            self.logger.info(f"Civitai搜索结果: {result}")

        except Exception as e:
            self.logger.error(f"Civitai搜索失败: {e}")

    def get_lora_statistics(self) -> Dict[str, Any]:
        """
        获取LoRA统计信息

        Returns:
            Dict[str, Any]: LoRA统计信息
        """
        try:
            all_models = list(self.lora_manager.lora_models.values())
            local_models = [m for m in all_models if m.is_local]
            active_models = [m for m in all_models if m.is_active]

            return {
                'total_models': len(all_models),
                'local_models': len(local_models),
                'active_models': len(active_models),
                'remote_models': len(all_models) - len(local_models),
                'categories': self._get_category_distribution(all_models),
                'average_rating': self._calculate_average_rating(all_models)
            }
        except Exception as e:
            self.logger.error(f"获取LoRA统计失败: {e}")
            return {}

    def _get_category_distribution(self, models: List) -> Dict[str, int]:
        """获取分类分布"""
        distribution = {}
        for model in models:
            category = getattr(model, 'category', None)
            if category:
                category_name = category.value if hasattr(category, 'value') else str(category)
                distribution[category_name] = distribution.get(category_name, 0) + 1
        return distribution

    def _calculate_average_rating(self, models: List) -> float:
        """计算平均评分"""
        ratings = [getattr(model, 'rating', 0) for model in models if (getattr(model, 'rating', None) or 0) > 0]
        return sum(ratings) / len(ratings) if ratings else 0.0


# 全局实例
_lora_integration_v2: Optional[LoRAIntegrationV2] = None


def get_lora_integration_v2() -> LoRAIntegrationV2:
    """获取 LoRA 集成器 V2 实例"""
    global _lora_integration_v2
    if _lora_integration_v2 is None:
        _lora_integration_v2 = LoRAIntegrationV2()
    return _lora_integration_v2
