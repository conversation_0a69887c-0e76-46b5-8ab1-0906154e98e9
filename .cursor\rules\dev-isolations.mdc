# LangBot 二次开发与原生代码隔离原则

<!-- 1. **目录结构隔离**  
   - 所有二次开发/自定义代码，统一放在专门的目录（如 `pkg/workers/blueraincoat/`、`pkg/plugin/blueraincoat/`、`pkg/blueraincoat/`、`pkg/command/operators/blueraincoat_*.py` 等）。
   - 原生代码（即官方仓库的代码）只放在官方目录，不直接修改。 -->

2. **代码注释标记**  
   - 每个二次开发的文件/函数/类顶部加上清晰注释，例如：  
     `# === 二次开发 by blueraincoat ===`  
     `# 2024-07-08 新增功能：xxx`

3. **Git 分支/提交规范**  
   - 二次开发全部在 feature/blueraincoat-xxx 分支开发，合并时只合并自定义目录。
   - 合并主分支时，只用官方代码覆盖原生目录，自定义目录不动。

4. **自动化脚本/工具**  
   - 用脚本定期对比本地和官方代码，自动生成差异清单（如 diff、check_dev_index.py）。
   - 可以用 find、diff、grep 等命令筛查所有自定义目录和文件。

5. **禁止直接修改官方核心目录**  
   - 团队开发规范：禁止直接修改 `pkg/core`、`pkg/pipeline`、`pkg/platform` 等官方目录，只允许在自定义目录开发。
   - 代码审查（Code Review）：合并前必须检查变更目录和文件，防止误改官方代码。
   - 自动化检查脚本：每次提交前自动运行脚本，检测是否有官方目录被修改，发现就阻止提交。
   - Git 钩子（pre-commit）：可写 pre-commit 钩子，自动检查变更范围。

6. **check_dev_index.py 脚本**  
   - 用于检查项目中哪些文件属于"二次开发"，哪些是"官方原生"，辅助团队管理和升级。
   - 建议定期运行，升级/合并官方代码前后都要用。

7. **备份与迁移**  
   - 每次升级/合并官方代码时，只覆盖官方目录，自定义目录不动。
   - 定期用 diff 或 check_dev_index.py 检查自定义内容，并做好备份。

8. **其他建议**  
   - 如需自动化检测和保护，可编写 pre-commit 钩子或自动化脚本。
   - 所有自定义代码都放在自定义目录，并加注释。
   - 升级官方代码时，优先保证自定义功能的可迁移性和独立性。

> 目的：保证官方代码可随时升级，二次开发功能独立、可迁移、可维护。

globs:
alwaysApply: true
---
